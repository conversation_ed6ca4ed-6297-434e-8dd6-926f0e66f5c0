{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/app/feasibility/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Calculator, TrendingUp, DollarSign, Zap, Calendar, Target, ArrowRight, Info } from 'lucide-react';\n\ninterface InvestmentInputs {\n  // Sistem Maliyetleri (USD)\n  ledModuleCost: number; // LED modül fiyatı (USD)\n  ledModuleCount: number; // LED modül sayısı\n  systemInfrastructure: number; // Sistem altyapısı (USD)\n  hydroponicEquipment: number; // Hidroponik ekipmanlar (USD)\n  installationCost: number; // Kurulum maliyeti (USD)\n\n  // İşletme Maliyetleri\n  electricityRate: number; // Elektrik fiyatı (USD cent/kWh)\n  seedCost: number; // Tohum maliyeti (USD/bitki)\n  nutrientCost: number; // Besin çözeltisi (USD/ay)\n  laborCost: number; // İşçilik (USD/ay)\n  maintenanceCost: number; // Bakım (USD/ay)\n\n  // Üretim Parametreleri\n  systemArea: number; // Sistem alanı (m²)\n  layerCount: number; // Kat sayısı\n  plantsPerM2: number; // m² başına bitki sayısı\n  harvestCycle: number; // Hasat döngüsü (gün)\n  yieldPerPlant: number; // Bitki başına verim (gram)\n\n  // Satış Parametreleri\n  sellingPrice: number; // Satış fiyatı (USD/kg)\n  marketLoss: number; // Pazar kaybı (%)\n\n  // Döviz Kuru\n  usdToLocalRate: number; // USD/TL kuru\n}\n\ninterface ROIResults {\n  // Yatırım\n  totalInvestment: number;\n  \n  // Aylık Değerler\n  monthlyProduction: number; // kg/ay\n  monthlyRevenue: number; // TL/ay\n  monthlyOperatingCost: number; // TL/ay\n  monthlyProfit: number; // TL/ay\n  \n  // ROI Metrikleri\n  paybackPeriod: number; // ay\n  roi12Months: number; // %\n  roi24Months: number; // %\n  roi36Months: number; // %\n  \n  // Birim Maliyetler\n  costPerKg: number; // TL/kg\n  profitMargin: number; // %\n  \n  // Enerji\n  monthlyEnergyConsumption: number; // kWh/ay\n  monthlyEnergyCost: number; // TL/ay\n}\n\nexport default function FeasibilityPage() {\n  const [inputs, setInputs] = useState<InvestmentInputs>({\n    // Sistem Maliyetleri (USD)\n    ledModuleCost: 28.5, // Samsung LM301H EVO modül fiyatı USD\n    ledModuleCount: 167, // Kozai hesaplamalarına göre 100m² için\n    systemInfrastructure: 2500, // Çelik konstrüksiyon, ventilasyon USD\n    hydroponicEquipment: 1500, // Tanklar, pompalar, sensörler USD\n    installationCost: 850, // Kurulum ve devreye alma USD\n\n    // İşletme Maliyetleri\n    electricityRate: 8.9, // USD cent/kWh (global ortalama)\n    seedCost: 0.005, // USD/bitki\n    nutrientCost: 85, // USD/ay\n    laborCost: 500, // USD/ay (2 kişi)\n    maintenanceCost: 100, // USD/ay\n\n    // Üretim Parametreleri\n    systemArea: 100, // m² (4 kat x 25 m²)\n    layerCount: 4,\n    plantsPerM2: 16, // 25cm x 25cm aralık (Kozai önerisi)\n    harvestCycle: 25, // gün (Kozai hızlı döngü)\n    yieldPerPlant: 180, // gram (ortalama marul ağırlığı)\n\n    // Satış Parametreleri\n    sellingPrice: 1.5, // USD/kg (organik marul)\n    marketLoss: 8, // % (nakliye, bozulma)\n\n    // Döviz Kuru\n    usdToLocalRate: 30 // USD/TL kuru\n  });\n\n  const [results, setResults] = useState<ROIResults>({\n    totalInvestment: 0,\n    monthlyProduction: 0,\n    monthlyRevenue: 0,\n    monthlyOperatingCost: 0,\n    monthlyProfit: 0,\n    paybackPeriod: 0,\n    roi12Months: 0,\n    roi24Months: 0,\n    roi36Months: 0,\n    costPerKg: 0,\n    profitMargin: 0,\n    monthlyEnergyConsumption: 0,\n    monthlyEnergyCost: 0\n  });\n\n  const [selectedScenario, setSelectedScenario] = useState<'conservative' | 'realistic' | 'optimistic'>('realistic');\n\n  // Hesaplama fonksiyonu\n  useEffect(() => {\n    // Toplam yatırım\n    const totalInvestment = \n      inputs.ledModuleCost * inputs.ledModuleCount +\n      inputs.systemInfrastructure +\n      inputs.hydroponicEquipment +\n      inputs.installationCost;\n\n    // Aylık üretim hesaplama\n    const totalPlants = inputs.systemArea * inputs.layerCount * inputs.plantsPerM2;\n    const cyclesPerMonth = 30 / inputs.harvestCycle;\n    const monthlyPlantHarvest = totalPlants * cyclesPerMonth;\n    const monthlyProduction = (monthlyPlantHarvest * inputs.yieldPerPlant) / 1000; // kg\n    const marketableProduction = monthlyProduction * (1 - inputs.marketLoss / 100);\n\n    // Aylık gelir\n    const monthlyRevenue = marketableProduction * inputs.sellingPrice;\n\n    // Aylık enerji tüketimi ve maliyeti\n    const ledPowerConsumption = inputs.ledModuleCount * 50; // 50W per module\n    const dailyEnergyConsumption = (ledPowerConsumption * 14) / 1000; // 14 saat çalışma, kWh\n    const monthlyEnergyConsumption = dailyEnergyConsumption * 30;\n    const monthlyEnergyCost = monthlyEnergyConsumption * inputs.electricityRate;\n\n    // Aylık işletme maliyeti\n    const monthlySeedCost = monthlyPlantHarvest * inputs.seedCost;\n    const monthlyOperatingCost = \n      monthlyEnergyCost +\n      monthlySeedCost +\n      inputs.nutrientCost +\n      inputs.laborCost +\n      inputs.maintenanceCost;\n\n    // Aylık kar\n    const monthlyProfit = monthlyRevenue - monthlyOperatingCost;\n\n    // ROI hesaplamaları\n    const paybackPeriod = totalInvestment / monthlyProfit;\n    const roi12Months = ((monthlyProfit * 12 - totalInvestment) / totalInvestment) * 100;\n    const roi24Months = ((monthlyProfit * 24 - totalInvestment) / totalInvestment) * 100;\n    const roi36Months = ((monthlyProfit * 36 - totalInvestment) / totalInvestment) * 100;\n\n    // Birim maliyetler\n    const costPerKg = monthlyOperatingCost / marketableProduction;\n    const profitMargin = ((inputs.sellingPrice - costPerKg) / inputs.sellingPrice) * 100;\n\n    setResults({\n      totalInvestment,\n      monthlyProduction: marketableProduction,\n      monthlyRevenue,\n      monthlyOperatingCost,\n      monthlyProfit,\n      paybackPeriod,\n      roi12Months,\n      roi24Months,\n      roi36Months,\n      costPerKg,\n      profitMargin,\n      monthlyEnergyConsumption,\n      monthlyEnergyCost\n    });\n  }, [inputs]);\n\n  const applyScenario = (scenario: string) => {\n    const scenarios = {\n      conservative: {\n        sellingPrice: 35, // TL/kg\n        yieldPerPlant: 150, // gram\n        marketLoss: 12, // %\n        electricityRate: 3.2, // TL/kWh\n        harvestCycle: 28 // gün\n      },\n      realistic: {\n        sellingPrice: 45, // TL/kg\n        yieldPerPlant: 180, // gram\n        marketLoss: 8, // %\n        electricityRate: 2.67, // TL/kWh\n        harvestCycle: 25 // gün\n      },\n      optimistic: {\n        sellingPrice: 55, // TL/kg\n        yieldPerPlant: 220, // gram\n        marketLoss: 5, // %\n        electricityRate: 2.2, // TL/kWh\n        harvestCycle: 22 // gün\n      }\n    };\n\n    const scenarioData = scenarios[scenario as keyof typeof scenarios];\n    setInputs(prev => ({ ...prev, ...scenarioData }));\n    setSelectedScenario(scenario as any);\n  };\n\n  const getROIColor = (roi: number) => {\n    if (roi >= 50) return 'text-green-600';\n    if (roi >= 20) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getPaybackColor = (months: number) => {\n    if (months <= 18) return 'text-green-600';\n    if (months <= 30) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Hidroponik Sistem Fizibilite Analizi\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            30 günlük döngülerde ROI hesaplama ve yatırım analizi\n          </p>\n        </div>\n\n        {/* Senaryo Seçimi */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Senaryo Seçimi</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {[\n              { key: 'conservative', name: 'Muhafazakar', desc: 'Düşük fiyat, yüksek maliyet', color: 'red' },\n              { key: 'realistic', name: 'Gerçekçi', desc: 'Mevcut piyasa koşulları', color: 'blue' },\n              { key: 'optimistic', name: 'İyimser', desc: 'Yüksek fiyat, düşük maliyet', color: 'green' }\n            ].map((scenario) => (\n              <button\n                key={scenario.key}\n                onClick={() => applyScenario(scenario.key)}\n                className={`p-4 rounded-lg border-2 transition-colors ${\n                  selectedScenario === scenario.key\n                    ? `border-${scenario.color}-500 bg-${scenario.color}-50`\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n              >\n                <h3 className=\"font-medium text-gray-900\">{scenario.name}</h3>\n                <p className=\"text-sm text-gray-600 mt-1\">{scenario.desc}</p>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Giriş Parametreleri */}\n          <div className=\"space-y-6\">\n            {/* Yatırım Maliyetleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <DollarSign className=\"w-5 h-5 mr-2 text-blue-600\" />\n                Yatırım Maliyetleri\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      LED Modül Fiyatı (TL)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.ledModuleCost}\n                      onChange={(e) => setInputs(prev => ({ ...prev, ledModuleCost: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      LED Modül Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.ledModuleCount}\n                      onChange={(e) => setInputs(prev => ({ ...prev, ledModuleCount: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Sistem Altyapısı (TL)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.systemInfrastructure}\n                    onChange={(e) => setInputs(prev => ({ ...prev, systemInfrastructure: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Hidroponik Ekipmanlar (TL)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.hydroponicEquipment}\n                    onChange={(e) => setInputs(prev => ({ ...prev, hydroponicEquipment: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Kurulum Maliyeti (TL)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.installationCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, installationCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* Üretim Parametreleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Target className=\"w-5 h-5 mr-2 text-green-600\" />\n                Üretim Parametreleri\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Sistem Alanı (m²)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.systemArea}\n                      onChange={(e) => setInputs(prev => ({ ...prev, systemArea: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Kat Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.layerCount}\n                      onChange={(e) => setInputs(prev => ({ ...prev, layerCount: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      m² Başına Bitki Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.plantsPerM2}\n                      onChange={(e) => setInputs(prev => ({ ...prev, plantsPerM2: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Hasat Döngüsü (gün)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.harvestCycle}\n                      onChange={(e) => setInputs(prev => ({ ...prev, harvestCycle: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Bitki Başına Verim (gram)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.yieldPerPlant}\n                      onChange={(e) => setInputs(prev => ({ ...prev, yieldPerPlant: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Satış Fiyatı (TL/kg)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.sellingPrice}\n                      onChange={(e) => setInputs(prev => ({ ...prev, sellingPrice: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Pazar Kaybı (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.marketLoss}\n                    onChange={(e) => setInputs(prev => ({ ...prev, marketLoss: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* İşletme Maliyetleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Zap className=\"w-5 h-5 mr-2 text-yellow-600\" />\n                İşletme Maliyetleri\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Elektrik Fiyatı (TL/kWh)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      value={inputs.electricityRate}\n                      onChange={(e) => setInputs(prev => ({ ...prev, electricityRate: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tohum Maliyeti (TL/bitki)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      value={inputs.seedCost}\n                      onChange={(e) => setInputs(prev => ({ ...prev, seedCost: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Besin Çözeltisi (TL/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.nutrientCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, nutrientCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    İşçilik Maliyeti (TL/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.laborCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, laborCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n                \n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Bakım Maliyeti (TL/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.maintenanceCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, maintenanceCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Sonuçlar */}\n          <div className=\"space-y-6\">\n            {/* ROI Özeti */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <TrendingUp className=\"w-5 h-5 mr-2 text-green-600\" />\n                ROI Analizi\n              </h2>\n              \n              <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                <div className=\"bg-blue-50 rounded-lg p-4\">\n                  <div className=\"text-sm text-blue-600 font-medium\">Toplam Yatırım</div>\n                  <div className=\"text-2xl font-bold text-blue-900\">\n                    ₺{results.totalInvestment.toLocaleString()}\n                  </div>\n                </div>\n                \n                <div className=\"bg-green-50 rounded-lg p-4\">\n                  <div className=\"text-sm text-green-600 font-medium\">Aylık Kar</div>\n                  <div className=\"text-2xl font-bold text-green-900\">\n                    ₺{results.monthlyProfit.toLocaleString()}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Geri Ödeme Süresi</span>\n                  <span className={`font-bold ${getPaybackColor(results.paybackPeriod)}`}>\n                    {results.paybackPeriod.toFixed(1)} ay\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">12 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi12Months)}`}>\n                    %{results.roi12Months.toFixed(1)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">24 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi24Months)}`}>\n                    %{results.roi24Months.toFixed(1)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">36 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi36Months)}`}>\n                    %{results.roi36Months.toFixed(1)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Üretim ve Maliyet Detayları */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Calculator className=\"w-5 h-5 mr-2 text-purple-600\" />\n                Üretim ve Maliyet Detayları\n              </h2>\n              \n              <div className=\"space-y-3\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Üretim</span>\n                  <span className=\"font-medium\">{results.monthlyProduction.toFixed(0)} kg</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Gelir</span>\n                  <span className=\"font-medium text-green-600\">₺{results.monthlyRevenue.toLocaleString()}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık İşletme Maliyeti</span>\n                  <span className=\"font-medium text-red-600\">₺{results.monthlyOperatingCost.toLocaleString()}</span>\n                </div>\n                \n                <hr className=\"my-2\" />\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Kg Başı Maliyet</span>\n                  <span className=\"font-medium\">₺{results.costPerKg.toFixed(2)}</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Kar Marjı</span>\n                  <span className=\"font-medium\">{results.profitMargin.toFixed(1)}%</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Enerji Tüketimi</span>\n                  <span className=\"font-medium\">{results.monthlyEnergyConsumption.toFixed(0)} kWh</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Enerji Maliyeti</span>\n                  <span className=\"font-medium\">₺{results.monthlyEnergyCost.toLocaleString()}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA4De,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACrD,2BAA2B;QAC3B,eAAe;QACf,gBAAgB;QAChB,sBAAsB;QACtB,qBAAqB;QACrB,kBAAkB;QAElB,sBAAsB;QACtB,iBAAiB;QACjB,UAAU;QACV,cAAc;QACd,WAAW;QACX,iBAAiB;QAEjB,uBAAuB;QACvB,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,cAAc;QACd,eAAe;QAEf,sBAAsB;QACtB,cAAc;QACd,YAAY;QAEZ,aAAa;QACb,gBAAgB,GAAG,cAAc;IACnC;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACjD,iBAAiB;QACjB,mBAAmB;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,eAAe;QACf,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW;QACX,cAAc;QACd,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAEtG,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,iBAAiB;YACjB,MAAM,kBACJ,OAAO,aAAa,GAAG,OAAO,cAAc,GAC5C,OAAO,oBAAoB,GAC3B,OAAO,mBAAmB,GAC1B,OAAO,gBAAgB;YAEzB,yBAAyB;YACzB,MAAM,cAAc,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,OAAO,WAAW;YAC9E,MAAM,iBAAiB,KAAK,OAAO,YAAY;YAC/C,MAAM,sBAAsB,cAAc;YAC1C,MAAM,oBAAoB,AAAC,sBAAsB,OAAO,aAAa,GAAI,MAAM,KAAK;YACpF,MAAM,uBAAuB,oBAAoB,CAAC,IAAI,OAAO,UAAU,GAAG,GAAG;YAE7E,cAAc;YACd,MAAM,iBAAiB,uBAAuB,OAAO,YAAY;YAEjE,oCAAoC;YACpC,MAAM,sBAAsB,OAAO,cAAc,GAAG,IAAI,iBAAiB;YACzE,MAAM,yBAAyB,AAAC,sBAAsB,KAAM,MAAM,uBAAuB;YACzF,MAAM,2BAA2B,yBAAyB;YAC1D,MAAM,oBAAoB,2BAA2B,OAAO,eAAe;YAE3E,yBAAyB;YACzB,MAAM,kBAAkB,sBAAsB,OAAO,QAAQ;YAC7D,MAAM,uBACJ,oBACA,kBACA,OAAO,YAAY,GACnB,OAAO,SAAS,GAChB,OAAO,eAAe;YAExB,YAAY;YACZ,MAAM,gBAAgB,iBAAiB;YAEvC,oBAAoB;YACpB,MAAM,gBAAgB,kBAAkB;YACxC,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,eAAe,IAAI,kBAAmB;YACjF,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,eAAe,IAAI,kBAAmB;YACjF,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,eAAe,IAAI,kBAAmB;YAEjF,mBAAmB;YACnB,MAAM,YAAY,uBAAuB;YACzC,MAAM,eAAe,AAAC,CAAC,OAAO,YAAY,GAAG,SAAS,IAAI,OAAO,YAAY,GAAI;YAEjF,WAAW;gBACT;gBACA,mBAAmB;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;YAChB,cAAc;gBACZ,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;YACA,WAAW;gBACT,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;YACA,YAAY;gBACV,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;QACF;QAEA,MAAM,eAAe,SAAS,CAAC,SAAmC;QAClE,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,YAAY;YAAC,CAAC;QAC/C,oBAAoB;IACtB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,IAAI,OAAO;QACzB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;8BAMvC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAgB,MAAM;oCAAe,MAAM;oCAA+B,OAAO;gCAAM;gCAC9F;oCAAE,KAAK;oCAAa,MAAM;oCAAY,MAAM;oCAA2B,OAAO;gCAAO;gCACrF;oCAAE,KAAK;oCAAc,MAAM;oCAAW,MAAM;oCAA+B,OAAO;gCAAQ;6BAC3F,CAAC,GAAG,CAAC,CAAC,yBACL,6LAAC;oCAEC,SAAS,IAAM,cAAc,SAAS,GAAG;oCACzC,WAAW,AAAC,6CAIX,OAHC,qBAAqB,SAAS,GAAG,GAC7B,AAAC,UAAkC,OAAzB,SAAS,KAAK,EAAC,YAAyB,OAAf,SAAS,KAAK,EAAC,SAClD;;sDAGN,6LAAC;4CAAG,WAAU;sDAA6B,SAAS,IAAI;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAA8B,SAAS,IAAI;;;;;;;mCATnD,SAAS,GAAG;;;;;;;;;;;;;;;;8BAezB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,aAAa;oEAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACtF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,cAAc;oEAC5B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACvF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,oBAAoB;4DAClC,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAC7F,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,mBAAmB;4DACjC,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAC5F,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,gBAAgB;4DAC9B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACzF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAOlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAGpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACnF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACnF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,WAAW;oEACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACpF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,YAAY;oEAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACrF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,aAAa;oEAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACtF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,YAAY;oEAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACrF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,UAAU;4DACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAOlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,eAAe;oEAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACxF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,QAAQ;oEACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACjF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,YAAY;4DAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACrF,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,SAAS;4DACvB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAClF,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,eAAe;4DAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACxF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAIxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;;gEAAmC;gEAC9C,QAAQ,eAAe,CAAC,cAAc;;;;;;;;;;;;;8DAI5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,6LAAC;4DAAI,WAAU;;gEAAoC;gEAC/C,QAAQ,aAAa,CAAC,cAAc;;;;;;;;;;;;;;;;;;;sDAK5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAAmD,OAAvC,gBAAgB,QAAQ,aAAa;;gEAChE,QAAQ,aAAa,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAItC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAIlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAIlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAIzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,iBAAiB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAA6B;gEAAE,QAAQ,cAAc,CAAC,cAAc;;;;;;;;;;;;;8DAGtF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAA2B;gEAAE,QAAQ,oBAAoB,CAAC,cAAc;;;;;;;;;;;;;8DAG1F,6LAAC;oDAAG,WAAU;;;;;;8DAEd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE,QAAQ,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAG5D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,YAAY,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,wBAAwB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAG7E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAc;gEAAE,QAAQ,iBAAiB,CAAC,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS1F;GA9hBwB;KAAA", "debugId": null}}, {"offset": {"line": 1402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return type.displayName || \"Context\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\"),\n      REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      react_stack_bottom_frame: function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React.react_stack_bottom_frame.bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,KAAK,WAAW,IAAI;YAC7B,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,0BAA0B,SAAU,iBAAiB;YACnD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,MAAM,wBAAwB,CAAC,IAAI,CAC9D,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA;;KAEO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/shared/src/utils.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/shared/src/utils.ts"], "sourcesContent": ["import { CamelToPascal } from './utility-types';\n\n/**\n * Converts string to kebab case\n *\n * @param {string} string\n * @returns {string} A kebabized string\n */\nexport const toKebabCase = (string: string) =>\n  string.replace(/([a-z0-9])([A-Z])/g, '$1-$2').toLowerCase();\n\n/**\n * Converts string to camel case\n *\n * @param {string} string\n * @returns {string} A camelized string\n */\nexport const toCamelCase = <T extends string>(string: T) =>\n  string.replace(/^([A-Z])|[\\s-_]+(\\w)/g, (match, p1, p2) =>\n    p2 ? p2.toUpperCase() : p1.toLowerCase(),\n  );\n\n/**\n * Converts string to pascal case\n *\n * @param {string} string\n * @returns {string} A pascalized string\n */\nexport const toPascalCase = <T extends string>(string: T): CamelToPascal<T> => {\n  const camelCase = toCamelCase(string);\n\n  return (camelCase.charAt(0).toUpperCase() + camelCase.slice(1)) as CamelToPascal<T>;\n};\n\n/**\n * Merges classes into a single string\n *\n * @param {array} classes\n * @returns {string} A string of classes\n */\nexport const mergeClasses = <ClassType = string | undefined | null>(...classes: ClassType[]) =>\n  classes\n    .filter((className, index, array) => {\n      return (\n        Boolean(className) &&\n        (className as string).trim() !== '' &&\n        array.indexOf(className) === index\n      );\n    })\n    .join(' ')\n    .trim();\n\n/**\n * Check if a component has an accessibility prop\n *\n * @param {object} props\n * @returns {boolean} Whether the component has an accessibility prop\n */\nexport const hasA11yProp = (props: Record<string, any>) => {\n  for (const prop in props) {\n    if (prop.startsWith('aria-') || prop === 'role' || prop === 'title') {\n      return true;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAC1B,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,OAAA,CAAQ,CAAsB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAE,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAQ/C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,CAAA,CAAmB,MAAA,CAC5C,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAyB,CAAC,OAAO,CAAI,CAAA,CAAA,CAAA,EAAA,CAClD,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAS9B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,CAAmB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAgC,CAAA,CAAA,CAAA,CAAA,CAAA;IACvE,MAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,YAAY,MAAM,CAAA;IAE5B,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,MAAA,CAAO,CAAC,CAAA,CAAE,WAAA,EAAgB,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,KAAA,CAAM,CAAC,CAAA;AAC/D,CAAA;AAQa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,CAAA,EAA2C,CAAA;;QAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACrE;;WAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CACG,MAAA,CAAO,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,OAAO,KAAU,CAAA,CAAA,CAAA,CAAA,CAAA;QAEjC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,CAAA,CAChB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAW,CACjC,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IAEjC,CAAC,CACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAG,CAAA,CAAA,CAAA,CACR,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA;;AAQG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,CAAC,CAAA,CAAA,CAAA,CAAA,CAA+B,CAAA,CAAA,CAAA,CAAA,CAAA;IACzD,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,QAAQ,KAAO,CAAA;QACxB,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,OAAS,CAAA,CAAA;YAC5D,OAAA,CAAA,CAAA,CAAA,CAAA;QAAA;IACT;AAEJ,CAAA", "debugId": null}}, {"offset": {"line": 1659, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/defaultAttributes.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/defaultAttributes.ts"], "sourcesContent": ["export default {\n  xmlns: 'http://www.w3.org/2000/svg',\n  width: 24,\n  height: 24,\n  viewBox: '0 0 24 24',\n  fill: 'none',\n  stroke: 'currentColor',\n  strokeWidth: 2,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA;IACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACT,CAAA,CAAA,CAAA,CAAM,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACN,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,EAAA,CAAA;IACb,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACf,cAAgB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AAClB,CAAA", "debugId": null}}, {"offset": {"line": 1684, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/Icon.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/Icon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport defaultAttributes from './defaultAttributes';\nimport { IconNode, LucideProps } from './types';\nimport { mergeClasses, hasA11yProp } from '@lucide/shared';\n\ninterface IconComponentProps extends LucideProps {\n  iconNode: IconNode;\n}\n\n/**\n * Lucide icon component\n *\n * @component Icon\n * @param {object} props\n * @param {string} props.color - The color of the icon\n * @param {number} props.size - The size of the icon\n * @param {number} props.strokeWidth - The stroke width of the icon\n * @param {boolean} props.absoluteStrokeWidth - Whether to use absolute stroke width\n * @param {string} props.className - The class name of the icon\n * @param {IconNode} props.children - The children of the icon\n * @param {IconNode} props.iconNode - The icon node of the icon\n *\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst Icon = forwardRef<SVGSVGElement, IconComponentProps>(\n  (\n    {\n      color = 'currentColor',\n      size = 24,\n      strokeWidth = 2,\n      absoluteStrokeWidth,\n      className = '',\n      children,\n      iconNode,\n      ...rest\n    },\n    ref,\n  ) =>\n    createElement(\n      'svg',\n      {\n        ref,\n        ...defaultAttributes,\n        width: size,\n        height: size,\n        stroke: color,\n        strokeWidth: absoluteStrokeWidth ? (Number(strokeWidth) * 24) / Number(size) : strokeWidth,\n        className: mergeClasses('lucide', className),\n        ...(!children && !hasA11yProp(rest) && { 'aria-hidden': 'true' }),\n        ...rest,\n      },\n      [\n        ...iconNode.map(([tag, attrs]) => createElement(tag, attrs)),\n        ...(Array.isArray(children) ? children : [children]),\n      ],\n    ),\n);\n\nexport default Icon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAwBA,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,qKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACX,QAWE,CAEA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAZA,EACE,CAAA,CAAA,CAAA,CAAA,CAAQ,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACR,CAAA,CAAA,CAAA,CAAO,GAAA,CAAA,CAAA,EACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAc,GAAA,CAAA,EACd,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,GAAA,CAAA,CAAA,EACZ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EACA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA;6KAIL,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACE,CAAA,CAAA,CAAA,CAAA,CAAA,EACA;QACE,CAAA,CAAA,CAAA;QACA,0KAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACH,CAAA,CAAA,CAAA,CAAA,CAAO,EAAA,CAAA,CAAA,CAAA,CAAA;QACP,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA;QACR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACR,WAAA,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAuB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAA,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAI,CAAA,CAAA,CAAA,CAAM,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO,CAAI,CAAA,CAAA,CAAA,CAAI,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAC/E,SAAA,CAAW,iLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,AAAa,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,SAAS,CAAA;QAC3C,CAAI,CAAA,CAAA,CAAC,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,iLAAC,cAAA,EAAY,CAAI,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAe,MAAO;QAAA,CAAA;QAC/D,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;IACL,CAAA,EACA;WACK,CAAS,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAI;gBAAC,CAAC,CAAK,CAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAM;qLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAc,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAK,CAAC,CAAA;;WACvD,CAAM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,CAAA,CAAA;YAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;SAAA;KAAA", "debugId": null}}, {"offset": {"line": 1728, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/createLucideIcon.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/createLucideIcon.ts"], "sourcesContent": ["import { createElement, forwardRef } from 'react';\nimport { mergeClasses, toKebabCase, toPascalCase } from '@lucide/shared';\nimport { IconNode, LucideProps } from './types';\nimport Icon from './Icon';\n\n/**\n * Create a Lucide icon component\n * @param {string} iconName\n * @param {array} iconNode\n * @returns {ForwardRefExoticComponent} LucideIcon\n */\nconst createLucideIcon = (iconName: string, iconNode: IconNode) => {\n  const Component = forwardRef<SVGSVGElement, LucideProps>(({ className, ...props }, ref) =>\n    createElement(Icon, {\n      ref,\n      iconNode,\n      className: mergeClasses(\n        `lucide-${toKebabCase(toPascalCase(iconName))}`,\n        `lucide-${iconName}`,\n        className,\n      ),\n      ...props,\n    }),\n  );\n\n  Component.displayName = toPascalCase(iconName);\n\n  return Component;\n};\n\nexport default createLucideIcon;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAWM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,gBAAA,CAAmB,CAAA,CAAA,CAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAkB,QAAuB,CAAA,CAAA,CAAA,CAAA,CAAA;IACjE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mKAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAuC,QAA0B,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAzB,CAAA,CAAE,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAS,CAAA,CAAA;iLACjF,gBAAA,4JAAc,UAAM,CAAA,CAAA;YAClB,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAW,kLAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EACT,AAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAmC,CAAA,sLAAnC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,kLAAY,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,KAAA,EAAA,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAC,CAAC,GAC7C,QAAU,EAAQ,CAAA,MAAR,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,GAClB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAEF,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACJ,CAAA;;IAGO,SAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,mLAAc,eAAA,EAAa,QAAQ,CAAA;IAEtC,OAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACT,CAAA", "debugId": null}}, {"offset": {"line": 1761, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/calculator.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/icons/calculator.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['rect', { width: '16', height: '20', x: '4', y: '2', rx: '2', key: '1nb95v' }],\n  ['line', { x1: '8', x2: '16', y1: '6', y2: '6', key: 'x4nwl0' }],\n  ['line', { x1: '16', x2: '16', y1: '14', y2: '18', key: 'wjye3r' }],\n  ['path', { d: 'M16 10h.01', key: '1m94wz' }],\n  ['path', { d: 'M12 10h.01', key: '1nrarc' }],\n  ['path', { d: 'M8 10h.01', key: '19clt8' }],\n  ['path', { d: 'M12 14h.01', key: '1etili' }],\n  ['path', { d: 'M8 14h.01', key: '6423bh' }],\n  ['path', { d: 'M12 18h.01', key: 'mhygvu' }],\n  ['path', { d: 'M8 18h.01', key: 'lrp35t' }],\n];\n\n/**\n * @component @name Calculator\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cmVjdCB3aWR0aD0iMTYiIGhlaWdodD0iMjAiIHg9IjQiIHk9IjIiIHJ4PSIyIiAvPgogIDxsaW5lIHgxPSI4IiB4Mj0iMTYiIHkxPSI2IiB5Mj0iNiIgLz4KICA8bGluZSB4MT0iMTYiIHgyPSIxNiIgeTE9IjE0IiB5Mj0iMTgiIC8+CiAgPHBhdGggZD0iTTE2IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTBoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDEwaC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMTRoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE0aC4wMSIgLz4KICA8cGF0aCBkPSJNMTIgMThoLjAxIiAvPgogIDxwYXRoIGQ9Ik04IDE4aC4wMSIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/calculator\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Calculator = createLucideIcon('calculator', __iconNode);\n\nexport default Calculator;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ;QAAA,CAAA;YAAE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAO;YAAM,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAG,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC9E;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAK,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAK;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC/D;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAc,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAa,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC5C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1869, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/trending-up.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1911, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/dollar-sign.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/icons/dollar-sign.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['line', { x1: '12', x2: '12', y1: '2', y2: '22', key: '7eqyqh' }],\n  ['path', { d: 'M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6', key: '1b0p4s' }],\n];\n\n/**\n * @component @name DollarSign\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8bGluZSB4MT0iMTIiIHgyPSIxMiIgeTE9IjIiIHkyPSIyMiIgLz4KICA8cGF0aCBkPSJNMTcgNUg5LjVhMy41IDMuNSAwIDAgMCAwIDdoNWEzLjUgMy41IDAgMCAxIDAgN0g2IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/dollar-sign\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst DollarSign = createLucideIcon('dollar-sign', __iconNode);\n\nexport default DollarSign;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAK,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjE;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAqD,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACpF;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1956, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/zap.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/icons/zap.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z',\n      key: '1xq2db',\n    },\n  ],\n];\n\n/**\n * @component @name Zap\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNCAxNGExIDEgMCAwIDEtLjc4LTEuNjNsOS45LTEwLjJhLjUuNSAwIDAgMSAuODYuNDZsLTEuOTIgNi4wMkExIDEgMCAwIDAgMTMgMTBoN2ExIDEgMCAwIDEgLjc4IDEuNjNsLTkuOSAxMC4yYS41LjUgMCAwIDEtLjg2LS40NmwxLjkyLTYuMDJBMSAxIDAgMCAwIDExIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/zap\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Zap = createLucideIcon('zap', __iconNode);\n\nexport default Zap;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}, {"offset": {"line": 1991, "column": 0}, "map": {"version": 3, "file": "turbopack:///[project]/node_modules/lucide-react/dist/esm/icons/target.js", "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/node_modules/lucide-react/src/icons/target.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['circle', { cx: '12', cy: '12', r: '10', key: '1mglay' }],\n  ['circle', { cx: '12', cy: '12', r: '6', key: '1vlfrh' }],\n  ['circle', { cx: '12', cy: '12', r: '2', key: '1c9p78' }],\n];\n\n/**\n * @component @name Target\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIxMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSI2IiAvPgogIDxjaXJjbGUgY3g9IjEyIiBjeT0iMTIiIHI9IjIiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/target\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Target = createLucideIcon('target', __iconNode);\n\nexport default Target;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACzD;QAAC,QAAU,CAAA;QAAA,CAAA;YAAE,EAAI,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA;YAAK,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAU;KAAA;IACxD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,2KAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAiB,AAAjB,CAAA,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "debugId": null}}]}