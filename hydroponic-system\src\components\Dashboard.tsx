'use client';

import { useState, useEffect } from 'react';
import { Thermometer, Droplets, Lightbulb, Zap, AlertTriangle, CheckCircle } from 'lucide-react';

interface SensorReading {
  temperature: number;
  humidity: number;
  ph: number;
  ec: number;
  ppfd: number;
  dli: number;
  co2: number;
}

export default function Dashboard() {
  const [sensorData, setSensorData] = useState<SensorReading>({
    temperature: 22.5,
    humidity: 65,
    ph: 6.1,
    ec: 1.6,
    ppfd: 275,
    dli: 14.2,
    co2: 850
  });

  const [alerts, setAlerts] = useState([
    { id: 1, type: 'warning', message: 'PPFD değeri optimal aralığın üstünde', time: '2 dakika önce' },
    { id: 2, type: 'info', message: 'LED dimmer %85 seviyesinde', time: '5 dakika önce' }
  ]);

  // Kozai parametrelerine göre durum kontrolü
  const getStatus = (value: number, min: number, max: number) => {
    if (value >= min && value <= max) return 'optimal';
    if (value < min * 0.9 || value > max * 1.1) return 'critical';
    return 'warning';
  };

  const parameters = [
    {
      name: 'Sıcaklık',
      value: sensorData.temperature,
      unit: '°C',
      min: 20,
      max: 24,
      icon: Thermometer,
      color: 'blue'
    },
    {
      name: 'Nem',
      value: sensorData.humidity,
      unit: '%',
      min: 60,
      max: 70,
      icon: Droplets,
      color: 'cyan'
    },
    {
      name: 'pH',
      value: sensorData.ph,
      unit: '',
      min: 5.8,
      max: 6.3,
      icon: Droplets,
      color: 'purple'
    },
    {
      name: 'EC',
      value: sensorData.ec,
      unit: 'mS/cm',
      min: 1.4,
      max: 1.8,
      icon: Zap,
      color: 'yellow'
    },
    {
      name: 'PPFD',
      value: sensorData.ppfd,
      unit: 'µmol/m²/s',
      min: 200,
      max: 300,
      icon: Lightbulb,
      color: 'orange'
    },
    {
      name: 'DLI',
      value: sensorData.dli,
      unit: 'mol/m²/gün',
      min: 12,
      max: 17,
      icon: Lightbulb,
      color: 'green'
    }
  ];

  useEffect(() => {
    // Simüle edilmiş gerçek zamanlı veri güncellemesi
    const interval = setInterval(() => {
      setSensorData(prev => ({
        ...prev,
        temperature: prev.temperature + (Math.random() - 0.5) * 0.2,
        humidity: prev.humidity + (Math.random() - 0.5) * 2,
        ppfd: prev.ppfd + (Math.random() - 0.5) * 10,
        dli: prev.dli + (Math.random() - 0.5) * 0.5
      }));
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="space-y-6">
      {/* Sistem Durumu */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Sistem Durumu</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {parameters.map((param) => {
            const Icon = param.icon;
            const status = getStatus(param.value, param.min, param.max);
            
            return (
              <div key={param.name} className="bg-gray-50 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Icon className={`w-5 h-5 text-${param.color}-500`} />
                    <span className="text-sm font-medium text-gray-700">{param.name}</span>
                  </div>
                  <div className={`w-3 h-3 rounded-full ${
                    status === 'optimal' ? 'bg-green-500' :
                    status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                </div>
                <div className="text-2xl font-bold text-gray-900">
                  {param.value.toFixed(1)}{param.unit}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Optimal: {param.min}-{param.max}{param.unit}
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Uyarılar */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Sistem Uyarıları</h2>
        <div className="space-y-3">
          {alerts.map((alert) => (
            <div key={alert.id} className={`flex items-start space-x-3 p-3 rounded-lg ${
              alert.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :
              alert.type === 'error' ? 'bg-red-50 border border-red-200' :
              'bg-blue-50 border border-blue-200'
            }`}>
              {alert.type === 'warning' ? (
                <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
              ) : alert.type === 'error' ? (
                <AlertTriangle className="w-5 h-5 text-red-500 mt-0.5" />
              ) : (
                <CheckCircle className="w-5 h-5 text-blue-500 mt-0.5" />
              )}
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-900">{alert.message}</p>
                <p className="text-xs text-gray-500">{alert.time}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Hızlı İstatistikler */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Günlük Enerji Tüketimi</h3>
          <p className="text-2xl font-bold text-gray-900">117.8 kWh</p>
          <p className="text-sm text-green-600">%5 azalma</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Aktif Bitki Sayısı</h3>
          <p className="text-2xl font-bold text-gray-900">1,247</p>
          <p className="text-sm text-blue-600">4 katlı sistem</p>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500 mb-2">Tahmini Hasat</h3>
          <p className="text-2xl font-bold text-gray-900">18 gün</p>
          <p className="text-sm text-purple-600">Marul Rex çeşidi</p>
        </div>
      </div>
    </div>
  );
}
