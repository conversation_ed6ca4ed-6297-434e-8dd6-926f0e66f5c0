import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link getDaysInMonth} function options.
 */
export interface GetDaysInMonthOptions extends ContextOptions<Date> {}
/**
 * @name getDaysInMonth
 * @category Month Helpers
 * @summary Get the number of days in a month of the given date.
 *
 * @description
 * Get the number of days in a month of the given date, considering the context if provided.
 *
 * @param date - The given date
 * @param options - An object with options
 *
 * @returns The number of days in a month
 *
 * @example
 * // How many days are in February 2000?
 * const result = getDaysInMonth(new Date(2000, 1))
 * //=> 29
 */
export declare function getDaysInMonth(
  date: DateArg<Date> & {},
  options?: GetDaysInMonthOptions | undefined,
): number;
