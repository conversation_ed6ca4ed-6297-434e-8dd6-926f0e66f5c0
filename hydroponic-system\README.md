# Hidroponik İç Mekan Tarım Sistemi

Bu proje, Toyoki Kozai'nin "Plant Factory" yaklaşımına dayalı olarak geliştirilmiş modern bir hidroponik iç mekan tarım yönetim sistemidir. Samsung LM301H EVO LED modülleri ile marul üretimi için optimize edilmiştir.

## 🌱 Özellikler

### Dashboard ve İzleme
- **Gerçek Zamanlı Sensör Verileri**: Sıcaklık, nem, pH, EC, CO₂, PPFD değerlerinin anlık takibi
- **Sistem Durumu**: Tüm bileşenlerin canlı durumu ve uyarı sistemi
- **Trend Analizi**: Geçmiş verilerin grafiksel analizi

### LED Işık Kontrolü
- **Samsung LM301H EVO Kontrolü**: PWM/dimmer ile hassas güç kontrolü
- **PPFD Hesaplama**: Mesafe ve güce göre otomatik PPFD hesaplama
- **DLI Optimizasyonu**: Günlük ışık integrali takibi
- **Fotoperiyot Yönetimi**: Otomatik zamanlama sistemi

### Üretim Takibi
- **Parti Yönetimi**: Ekim, büyüme ve hasat döngülerinin takibi
- **Çeşit Bazlı Takip**: Marul Rex, Butterhead, Lollo Rosso gibi çeşitlerin ayrı takibi
- **Hasat Planlama**: 20-25 günlük hızlı döngü optimizasyonu

### Enerji ve Maliyet Analizi
- **Elektrik Tüketimi**: Gerçek zamanlı güç tüketimi hesaplama
- **Maliyet Analizi**: Bitki başı maliyet hesaplama
- **Optimizasyon Modları**: Performans, dengeli ve tasarruf modları
- **CO₂ Emisyon Takibi**: Çevresel etki analizi

### Sensör Entegrasyonu
- **Çoklu Sensör Desteği**: Sıcaklık, nem, pH, EC, CO₂, hava akışı sensörleri
- **Gerçek Zamanlı Grafik**: Recharts ile interaktif veri görselleştirme
- **Sensör Durumu**: Çevrimiçi/çevrimdışı durum takibi
- **Uyarı Sistemi**: Kritik değerler için otomatik uyarılar

### Otomasyon Sistemi
- **Kozai Parametreleri**: Bilimsel verilere dayalı otomatik kontrol
- **Akıllı Kurallar**: Koşul-eylem bazlı otomasyon kuralları
- **Sistem Entegrasyonu**: LED, ventilasyon, sulama, ısıtma/soğutma kontrolü
- **Öncelik Sistemi**: Kritik, orta ve düşük öncelikli kurallar

## 🚀 Teknolojiler

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Headless UI, Lucide React
- **Charts**: Recharts
- **Icons**: Lucide React

## 📊 Kozai Parametreleri

### Optimal Değerler (Marul için)
- **PPFD**: 200-300 µmol/m²/s
- **DLI**: 12-17 mol/m²/gün
- **Sıcaklık**: Gündüz 20-24°C, Gece 18-20°C
- **Nem**: 60-70%
- **pH**: 5.8-6.3
- **EC**: 1.4-1.8 mS/cm
- **CO₂**: 700-1000 ppm
- **Fotoperiyot**: 14-16 saat

### Sistem Özellikleri
- **Alan**: 100 m² (4 kat × 25 m²)
- **LED Modülleri**: 167 adet Samsung LM301H EVO
- **Güç Tüketimi**: 8.4 kW (maksimum)
- **Üretim Kapasitesi**: 1600 bitki/ay
- **Hasat Döngüsü**: 20-25 gün

## 🛠️ Kurulum

1. **Projeyi klonlayın**:
```bash
git clone [repository-url]
cd hydroponic-system
```

2. **Bağımlılıkları yükleyin**:
```bash
npm install
```

3. **Geliştirme sunucusunu başlatın**:
```bash
npm run dev
```

4. **Tarayıcıda açın**: http://localhost:3000

## 📱 Kullanım

### Dashboard
- Ana sayfada sistem durumunu görüntüleyin
- Gerçek zamanlı sensör verilerini takip edin
- Uyarıları ve sistem durumunu kontrol edin

### LED Kontrolü
- LED yoğunluğunu ayarlayın (%0-100)
- LED mesafesini değiştirin (20-50 cm)
- Fotoperiyot süresini belirleyin (8-18 saat)
- Otomatik/manuel mod arasında geçiş yapın

### Üretim Takibi
- Yeni parti ekleyin
- Mevcut partilerin durumunu görüntüleyin
- Hasat tarihlerini takip edin

### Enerji Analizi
- Günlük/aylık tüketimi görüntüleyin
- Optimizasyon modları arasında seçim yapın
- Tasarruf potansiyelini analiz edin

### Sensör Verileri
- Gerçek zamanlı grafikleri inceleyin
- Sensör durumlarını kontrol edin
- Geçmiş verileri analiz edin

### Otomasyon
- Kuralları aktif/pasif yapın
- Son işlemleri görüntüleyin
- Sistem bileşenlerinin durumunu takip edin

## 🔧 Yapılandırma

### Sensör Ayarları
Sensör konfigürasyonları `src/components/SensorData.tsx` dosyasında düzenlenebilir.

### LED Parametreleri
LED hesaplamaları `src/components/LEDControl.tsx` dosyasında Samsung LM301H EVO spesifikasyonlarına göre ayarlanmıştır.

### Otomasyon Kuralları
Otomasyon kuralları `src/components/AutomationSystem.tsx` dosyasında Kozai parametrelerine göre tanımlanmıştır.

## 📈 Performans Metrikleri

### Üretim Hedefleri
- **Hasat Döngüsü**: 20-25 gün
- **Bitki Başı Maliyet**: ~5.90 TL (optimal koşullarda)
- **Enerji Verimliliği**: 2.975 µmol/J (Samsung LM301H EVO)
- **Üretim Kapasitesi**: 16 bitki/m²

### Enerji Optimizasyonu
- **Tasarruf Modu**: %20 enerji tasarrufu
- **Dengeli Mod**: Optimal verim/enerji dengesi
- **Performans Modu**: Maksimum üretim

## 🤝 Katkıda Bulunma

1. Fork yapın
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Commit yapın (`git commit -m 'Add amazing feature'`)
4. Push yapın (`git push origin feature/amazing-feature`)
5. Pull Request açın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 📚 Referanslar

- Kozai, T. (2013). Plant Factory: An Indoor Vertical Farming System for Efficient Quality Food Production
- Samsung Horticulture LED LM301H EVO Datasheet
- Hydroponics and Protected Cultivation Research

## 🆘 Destek

Sorularınız için issue açabilir veya iletişime geçebilirsiniz.

---

**Not**: Bu sistem Toyoki Kozai'nin bilimsel araştırmalarına dayalı olarak geliştirilmiştir ve gerçek hidroponik sistemlerde kullanım için tasarlanmıştır.
