'use client';

import { useState } from 'react';
import { Leaf, Calendar, TrendingUp, Package, Plus, Eye } from 'lucide-react';

interface PlantBatch {
  id: string;
  variety: string;
  plantedDate: string;
  expectedHarvest: string;
  currentStage: string;
  plantCount: number;
  layer: number;
  progress: number;
  status: 'healthy' | 'warning' | 'critical';
}

export default function ProductionTracker() {
  const [batches, setBatches] = useState<PlantBatch[]>([
    {
      id: 'B001',
      variety: 'Marul Rex',
      plantedDate: '2024-01-15',
      expectedHarvest: '2024-02-08',
      currentStage: 'Büyüme',
      plantCount: 320,
      layer: 1,
      progress: 75,
      status: 'healthy'
    },
    {
      id: 'B002',
      variety: 'Butterhead',
      plantedDate: '2024-01-20',
      expectedHarvest: '2024-02-13',
      currentStage: 'Gelişim',
      plantCount: 280,
      layer: 2,
      progress: 60,
      status: 'healthy'
    },
    {
      id: 'B003',
      variety: 'Marul Rex',
      plantedDate: '2024-01-25',
      expectedHarvest: '2024-02-18',
      currentStage: 'Fide',
      plantCount: 300,
      layer: 3,
      progress: 35,
      status: 'warning'
    },
    {
      id: 'B004',
      variety: 'Lollo Rosso',
      plantedDate: '2024-01-30',
      expectedHarvest: '2024-02-23',
      currentStage: 'Çimlenme',
      plantCount: 250,
      layer: 4,
      progress: 15,
      status: 'healthy'
    }
  ]);

  const [showAddForm, setShowAddForm] = useState(false);
  const [newBatch, setNewBatch] = useState({
    variety: '',
    plantCount: '',
    layer: ''
  });

  const varieties = ['Marul Rex', 'Butterhead', 'Lollo Rosso', 'Iceberg', 'Romaine'];
  const stages = ['Çimlenme', 'Fide', 'Gelişim', 'Büyüme', 'Hasat Hazır'];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 60) return 'bg-blue-500';
    if (progress >= 40) return 'bg-yellow-500';
    return 'bg-gray-500';
  };

  const calculateDaysRemaining = (harvestDate: string) => {
    const today = new Date();
    const harvest = new Date(harvestDate);
    const diffTime = harvest.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const handleAddBatch = () => {
    if (newBatch.variety && newBatch.plantCount && newBatch.layer) {
      const today = new Date();
      const harvestDate = new Date(today);
      harvestDate.setDate(today.getDate() + 24); // 24 günlük döngü

      const batch: PlantBatch = {
        id: `B${String(batches.length + 1).padStart(3, '0')}`,
        variety: newBatch.variety,
        plantedDate: today.toISOString().split('T')[0],
        expectedHarvest: harvestDate.toISOString().split('T')[0],
        currentStage: 'Çimlenme',
        plantCount: parseInt(newBatch.plantCount),
        layer: parseInt(newBatch.layer),
        progress: 5,
        status: 'healthy'
      };

      setBatches([...batches, batch]);
      setNewBatch({ variety: '', plantCount: '', layer: '' });
      setShowAddForm(false);
    }
  };

  const totalPlants = batches.reduce((sum, batch) => sum + batch.plantCount, 0);
  const readyToHarvest = batches.filter(batch => batch.progress >= 90).length;
  const averageProgress = batches.reduce((sum, batch) => sum + batch.progress, 0) / batches.length;

  return (
    <div className="space-y-6">
      {/* Özet Kartları */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Leaf className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Toplam Bitki</p>
              <p className="text-2xl font-bold text-gray-900">{totalPlants.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Package className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Aktif Parti</p>
              <p className="text-2xl font-bold text-gray-900">{batches.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Calendar className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Hasat Hazır</p>
              <p className="text-2xl font-bold text-gray-900">{readyToHarvest}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-500">Ortalama İlerleme</p>
              <p className="text-2xl font-bold text-gray-900">{averageProgress.toFixed(0)}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Parti Listesi */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-gray-900">Üretim Partileri</h2>
            <button
              onClick={() => setShowAddForm(true)}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
            >
              <Plus className="w-4 h-4" />
              <span>Yeni Parti</span>
            </button>
          </div>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Parti ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Çeşit
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Ekim Tarihi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hasat Tarihi
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Aşama
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Bitki Sayısı
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Kat
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İlerleme
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Durum
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  İşlemler
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {batches.map((batch) => (
                <tr key={batch.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    {batch.id}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {batch.variety}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(batch.plantedDate).toLocaleDateString('tr-TR')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(batch.expectedHarvest).toLocaleDateString('tr-TR')}
                    <div className="text-xs text-gray-500">
                      {calculateDaysRemaining(batch.expectedHarvest)} gün kaldı
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {batch.currentStage}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {batch.plantCount.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    Kat {batch.layer}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${getProgressColor(batch.progress)}`}
                          style={{ width: `${batch.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">{batch.progress}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(batch.status)}`}>
                      {batch.status === 'healthy' ? 'Sağlıklı' : 
                       batch.status === 'warning' ? 'Dikkat' : 'Kritik'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Yeni Parti Ekleme Formu */}
      {showAddForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Yeni Parti Ekle</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Marul Çeşidi
                </label>
                <select
                  value={newBatch.variety}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, variety: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seçiniz</option>
                  {varieties.map(variety => (
                    <option key={variety} value={variety}>{variety}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Bitki Sayısı
                </label>
                <input
                  type="number"
                  value={newBatch.plantCount}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, plantCount: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                  placeholder="Örn: 300"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Kat
                </label>
                <select
                  value={newBatch.layer}
                  onChange={(e) => setNewBatch(prev => ({ ...prev, layer: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  <option value="">Seçiniz</option>
                  <option value="1">Kat 1</option>
                  <option value="2">Kat 2</option>
                  <option value="3">Kat 3</option>
                  <option value="4">Kat 4</option>
                </select>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleAddBatch}
                className="flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700"
              >
                Parti Ekle
              </button>
              <button
                onClick={() => setShowAddForm(false)}
                className="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400"
              >
                İptal
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
