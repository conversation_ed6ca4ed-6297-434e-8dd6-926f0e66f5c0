'use client';

import { useState, useEffect } from 'react';
import { Calculator, TrendingUp, DollarSign, Zap, Calendar, Target, ArrowRight, Info } from 'lucide-react';

interface InvestmentInputs {
  // Sistem Maliyetleri (USD)
  ledModuleCost: number; // LED modül fiyatı (USD)
  ledModuleCount: number; // LED modül sayısı
  systemInfrastructure: number; // Sistem altyapısı (USD)
  hydroponicEquipment: number; // Hidroponik ekipmanlar (USD)
  installationCost: number; // Kurulum maliyeti (USD)

  // İşletme Maliyetleri
  electricityRate: number; // Elektrik fiyatı (USD cent/kWh)
  seedCost: number; // Tohum maliyeti (USD/bitki)
  nutrientCost: number; // Besin çözeltisi (USD/ay)
  laborCost: number; // İşçilik (USD/ay)
  maintenanceCost: number; // Bakım (USD/ay)

  // Üretim Parametreleri
  systemArea: number; // Sistem alanı (m²)
  layerCount: number; // Kat sayısı
  plantsPerM2: number; // m² başına bitki sayısı
  harvestCycle: number; // Hasat döngüsü (gün)
  yieldPerPlant: number; // Bitki başına verim (gram)

  // Satış Parametreleri
  sellingPrice: number; // Satış fiyatı (USD/kg)
  marketLoss: number; // Pazar kaybı (%)

  // Döviz Kuru
  usdToLocalRate: number; // USD/TL kuru
}

interface ROIResults {
  // Yatırım
  totalInvestment: number;
  
  // Aylık Değerler
  monthlyProduction: number; // kg/ay
  monthlyRevenue: number; // TL/ay
  monthlyOperatingCost: number; // TL/ay
  monthlyProfit: number; // TL/ay
  
  // ROI Metrikleri
  paybackPeriod: number; // ay
  roi12Months: number; // %
  roi24Months: number; // %
  roi36Months: number; // %
  
  // Birim Maliyetler
  costPerKg: number; // TL/kg
  profitMargin: number; // %
  
  // Enerji
  monthlyEnergyConsumption: number; // kWh/ay
  monthlyEnergyCost: number; // TL/ay
}

export default function FeasibilityPage() {
  const [inputs, setInputs] = useState<InvestmentInputs>({
    // Sistem Maliyetleri (USD)
    ledModuleCost: 28.5, // Samsung LM301H EVO modül fiyatı USD
    ledModuleCount: 167, // Kozai hesaplamalarına göre 100m² için
    systemInfrastructure: 2500, // Çelik konstrüksiyon, ventilasyon USD
    hydroponicEquipment: 1500, // Tanklar, pompalar, sensörler USD
    installationCost: 850, // Kurulum ve devreye alma USD

    // İşletme Maliyetleri
    electricityRate: 8.9, // USD cent/kWh (global ortalama)
    seedCost: 0.005, // USD/bitki
    nutrientCost: 85, // USD/ay
    laborCost: 500, // USD/ay (2 kişi)
    maintenanceCost: 100, // USD/ay

    // Üretim Parametreleri
    systemArea: 100, // m² (4 kat x 25 m²)
    layerCount: 4,
    plantsPerM2: 16, // 25cm x 25cm aralık (Kozai önerisi)
    harvestCycle: 25, // gün (Kozai hızlı döngü)
    yieldPerPlant: 180, // gram (ortalama marul ağırlığı)

    // Satış Parametreleri
    sellingPrice: 1.5, // USD/kg (organik marul)
    marketLoss: 8, // % (nakliye, bozulma)

    // Döviz Kuru
    usdToLocalRate: 30 // USD/TL kuru
  });

  const [results, setResults] = useState<ROIResults>({
    totalInvestment: 0,
    monthlyProduction: 0,
    monthlyRevenue: 0,
    monthlyOperatingCost: 0,
    monthlyProfit: 0,
    paybackPeriod: 0,
    roi12Months: 0,
    roi24Months: 0,
    roi36Months: 0,
    costPerKg: 0,
    profitMargin: 0,
    monthlyEnergyConsumption: 0,
    monthlyEnergyCost: 0
  });

  const [selectedScenario, setSelectedScenario] = useState<'conservative' | 'realistic' | 'optimistic'>('realistic');

  // Samsung LM301H EVO LED modül hesaplamaları (Kozai metodolojisine göre)
  const calculateLEDCoverage = (moduleCount: number) => {
    // Her modül 0.6m² kaplar (Kozai'ye göre optimal PPFD için)
    return moduleCount * 0.6;
  };

  const calculateModuleCount = (area: number) => {
    // 0.6m² başına 1 modül (Kozai PPFD 250-300 µmol/m²/s için)
    return Math.ceil(area / 0.6);
  };

  // Alan değiştiğinde modül sayısını güncelle
  useEffect(() => {
    const totalArea = inputs.systemArea * inputs.layerCount;
    const requiredModules = calculateModuleCount(totalArea);
    if (requiredModules !== inputs.ledModuleCount) {
      setInputs(prev => ({ ...prev, ledModuleCount: requiredModules }));
    }
  }, [inputs.systemArea, inputs.layerCount]);

  // Hesaplama fonksiyonu
  useEffect(() => {
    // Toplam yatırım (USD)
    const totalInvestmentUSD =
      inputs.ledModuleCost * inputs.ledModuleCount +
      inputs.systemInfrastructure +
      inputs.hydroponicEquipment +
      inputs.installationCost;

    // Aylık üretim hesaplama (Kozai parametrelerine göre)
    const totalPlants = inputs.systemArea * inputs.layerCount * inputs.plantsPerM2;
    const cyclesPerMonth = 30 / inputs.harvestCycle;
    const monthlyPlantHarvest = totalPlants * cyclesPerMonth;
    const monthlyProduction = (monthlyPlantHarvest * inputs.yieldPerPlant) / 1000; // kg
    const marketableProduction = monthlyProduction * (1 - inputs.marketLoss / 100);

    // Aylık gelir (USD)
    const monthlyRevenue = marketableProduction * inputs.sellingPrice;

    // Aylık enerji tüketimi ve maliyeti (Samsung LM301H EVO: 50W/modül)
    const ledPowerConsumption = inputs.ledModuleCount * 50; // 50W per module
    const dailyEnergyConsumption = (ledPowerConsumption * 14) / 1000; // 14 saat çalışma (Kozai önerisi), kWh
    const monthlyEnergyConsumption = dailyEnergyConsumption * 30;
    const monthlyEnergyCost = monthlyEnergyConsumption * (inputs.electricityRate / 100); // cent'ten USD'ye

    // Aylık işletme maliyeti (USD)
    const monthlySeedCost = monthlyPlantHarvest * inputs.seedCost;
    const monthlyOperatingCost =
      monthlyEnergyCost +
      monthlySeedCost +
      inputs.nutrientCost +
      inputs.laborCost +
      inputs.maintenanceCost;

    // Aylık kar (USD)
    const monthlyProfit = monthlyRevenue - monthlyOperatingCost;

    // ROI hesaplamaları
    const paybackPeriod = monthlyProfit > 0 ? totalInvestmentUSD / monthlyProfit : 0;
    const roi12Months = ((monthlyProfit * 12 - totalInvestmentUSD) / totalInvestmentUSD) * 100;
    const roi24Months = ((monthlyProfit * 24 - totalInvestmentUSD) / totalInvestmentUSD) * 100;
    const roi36Months = ((monthlyProfit * 36 - totalInvestmentUSD) / totalInvestmentUSD) * 100;

    // Birim maliyetler
    const costPerKg = marketableProduction > 0 ? monthlyOperatingCost / marketableProduction : 0;
    const profitMargin = inputs.sellingPrice > 0 ? ((inputs.sellingPrice - costPerKg) / inputs.sellingPrice) * 100 : 0;

    setResults({
      totalInvestment: totalInvestmentUSD,
      monthlyProduction: marketableProduction,
      monthlyRevenue,
      monthlyOperatingCost,
      monthlyProfit,
      paybackPeriod,
      roi12Months,
      roi24Months,
      roi36Months,
      costPerKg,
      profitMargin,
      monthlyEnergyConsumption,
      monthlyEnergyCost
    });
  }, [inputs]);

  const applyScenario = (scenario: string) => {
    const scenarios = {
      conservative: {
        sellingPrice: 1.2, // USD/kg
        yieldPerPlant: 150, // gram
        marketLoss: 12, // %
        electricityRate: 12.5, // USD cent/kWh
        harvestCycle: 28 // gün
      },
      realistic: {
        sellingPrice: 1.5, // USD/kg
        yieldPerPlant: 180, // gram
        marketLoss: 8, // %
        electricityRate: 8.9, // USD cent/kWh
        harvestCycle: 25 // gün
      },
      optimistic: {
        sellingPrice: 1.8, // USD/kg
        yieldPerPlant: 220, // gram
        marketLoss: 5, // %
        electricityRate: 6.5, // USD cent/kWh
        harvestCycle: 22 // gün
      }
    };

    const scenarioData = scenarios[scenario as keyof typeof scenarios];
    setInputs(prev => ({ ...prev, ...scenarioData }));
    setSelectedScenario(scenario as any);
  };

  const getROIColor = (roi: number) => {
    if (roi >= 50) return 'text-green-600';
    if (roi >= 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPaybackColor = (months: number) => {
    if (months <= 18) return 'text-green-600';
    if (months <= 30) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Hidroponik Sistem Fizibilite Analizi
          </h1>
          <p className="text-lg text-gray-600">
            Kozai metodolojisi ile 30 günlük döngülerde ROI hesaplama (USD bazlı)
          </p>
          <div className="mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-4xl mx-auto">
            <div className="flex items-start space-x-3">
              <Info className="w-5 h-5 text-blue-600 mt-0.5" />
              <div className="text-left">
                <h3 className="font-medium text-blue-900 mb-2">Samsung LM301H EVO LED Hesaplamaları</h3>
                <div className="text-sm text-blue-800 space-y-1">
                  <p>• Her modül 0.6m² alanı kaplar (Kozai PPFD 250-300 µmol/m²/s için optimal)</p>
                  <p>• Güç tüketimi: 50W/modül, PPE: 2.975 µmol/J</p>
                  <p>• Toplam alan: {inputs.systemArea * inputs.layerCount}m² = {inputs.ledModuleCount} modül gerekli</p>
                  <p>• Fotoperiyot: 14 saat/gün (Kozai önerisi)</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Senaryo Seçimi */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Senaryo Seçimi</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[
              { key: 'conservative', name: 'Muhafazakar', desc: 'Düşük fiyat, yüksek maliyet', color: 'red' },
              { key: 'realistic', name: 'Gerçekçi', desc: 'Mevcut piyasa koşulları', color: 'blue' },
              { key: 'optimistic', name: 'İyimser', desc: 'Yüksek fiyat, düşük maliyet', color: 'green' }
            ].map((scenario) => (
              <button
                key={scenario.key}
                onClick={() => applyScenario(scenario.key)}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  selectedScenario === scenario.key
                    ? `border-${scenario.color}-500 bg-${scenario.color}-50`
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <h3 className="font-medium text-gray-900">{scenario.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{scenario.desc}</p>
              </button>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Giriş Parametreleri */}
          <div className="space-y-6">
            {/* Yatırım Maliyetleri */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <DollarSign className="w-5 h-5 mr-2 text-blue-600" />
                Yatırım Maliyetleri (USD)
              </h2>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      LED Modül Fiyatı (USD)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={inputs.ledModuleCost}
                      onChange={(e) => setInputs(prev => ({ ...prev, ledModuleCost: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="text-xs text-gray-500 mt-1">Samsung LM301H EVO</div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      LED Modül Sayısı
                    </label>
                    <input
                      type="number"
                      value={inputs.ledModuleCount}
                      onChange={(e) => {
                        const newCount = Number(e.target.value);
                        const newArea = calculateLEDCoverage(newCount);
                        setInputs(prev => ({
                          ...prev,
                          ledModuleCount: newCount,
                          systemArea: Math.round(newArea / prev.layerCount)
                        }));
                      }}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      Kapladığı alan: {calculateLEDCoverage(inputs.ledModuleCount).toFixed(1)}m²
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sistem Altyapısı (USD)
                  </label>
                  <input
                    type="number"
                    value={inputs.systemInfrastructure}
                    onChange={(e) => setInputs(prev => ({ ...prev, systemInfrastructure: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="text-xs text-gray-500 mt-1">Çelik konstrüksiyon, ventilasyon</div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Hidroponik Ekipmanlar (USD)
                  </label>
                  <input
                    type="number"
                    value={inputs.hydroponicEquipment}
                    onChange={(e) => setInputs(prev => ({ ...prev, hydroponicEquipment: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="text-xs text-gray-500 mt-1">Tanklar, pompalar, sensörler</div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Kurulum Maliyeti (USD)
                  </label>
                  <input
                    type="number"
                    value={inputs.installationCost}
                    onChange={(e) => setInputs(prev => ({ ...prev, installationCost: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="text-xs text-gray-500 mt-1">Devreye alma ve test</div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    USD/TL Kuru
                  </label>
                  <input
                    type="number"
                    step="0.1"
                    value={inputs.usdToLocalRate}
                    onChange={(e) => setInputs(prev => ({ ...prev, usdToLocalRate: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    Toplam yatırım: ₺{(results.totalInvestment * inputs.usdToLocalRate).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Üretim Parametreleri */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2 text-green-600" />
                Üretim Parametreleri
              </h2>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Sistem Alanı (m²)
                    </label>
                    <input
                      type="number"
                      value={inputs.systemArea}
                      onChange={(e) => setInputs(prev => ({ ...prev, systemArea: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Kat Sayısı
                    </label>
                    <input
                      type="number"
                      value={inputs.layerCount}
                      onChange={(e) => setInputs(prev => ({ ...prev, layerCount: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      m² Başına Bitki Sayısı
                    </label>
                    <input
                      type="number"
                      value={inputs.plantsPerM2}
                      onChange={(e) => setInputs(prev => ({ ...prev, plantsPerM2: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Hasat Döngüsü (gün)
                    </label>
                    <input
                      type="number"
                      value={inputs.harvestCycle}
                      onChange={(e) => setInputs(prev => ({ ...prev, harvestCycle: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Bitki Başına Verim (gram)
                    </label>
                    <input
                      type="number"
                      value={inputs.yieldPerPlant}
                      onChange={(e) => setInputs(prev => ({ ...prev, yieldPerPlant: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Satış Fiyatı (USD/kg)
                    </label>
                    <input
                      type="number"
                      step="0.1"
                      value={inputs.sellingPrice}
                      onChange={(e) => setInputs(prev => ({ ...prev, sellingPrice: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="text-xs text-gray-500 mt-1">Organik marul pazar fiyatı</div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Pazar Kaybı (%)
                  </label>
                  <input
                    type="number"
                    value={inputs.marketLoss}
                    onChange={(e) => setInputs(prev => ({ ...prev, marketLoss: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>

            {/* İşletme Maliyetleri */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-600" />
                İşletme Maliyetleri
              </h2>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Elektrik Fiyatı (USD cent/kWh)
                    </label>
                    <input
                      type="number"
                      step="0.01"
                      value={inputs.electricityRate}
                      onChange={(e) => setInputs(prev => ({ ...prev, electricityRate: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      Aylık enerji maliyeti: ${results.monthlyEnergyCost.toFixed(2)}
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Tohum Maliyeti (USD/bitki)
                    </label>
                    <input
                      type="number"
                      step="0.001"
                      value={inputs.seedCost}
                      onChange={(e) => setInputs(prev => ({ ...prev, seedCost: Number(e.target.value) }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Besin Çözeltisi (USD/ay)
                  </label>
                  <input
                    type="number"
                    value={inputs.nutrientCost}
                    onChange={(e) => setInputs(prev => ({ ...prev, nutrientCost: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    İşçilik Maliyeti (USD/ay)
                  </label>
                  <input
                    type="number"
                    value={inputs.laborCost}
                    onChange={(e) => setInputs(prev => ({ ...prev, laborCost: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="text-xs text-gray-500 mt-1">2 kişi için aylık maaş</div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bakım Maliyeti (USD/ay)
                  </label>
                  <input
                    type="number"
                    value={inputs.maintenanceCost}
                    onChange={(e) => setInputs(prev => ({ ...prev, maintenanceCost: Number(e.target.value) }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Sonuçlar */}
          <div className="space-y-6">
            {/* ROI Özeti */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                ROI Analizi (USD)
              </h2>

              <div className="grid grid-cols-2 gap-4 mb-6">
                <div className="bg-blue-50 rounded-lg p-4">
                  <div className="text-sm text-blue-600 font-medium">Toplam Yatırım</div>
                  <div className="text-2xl font-bold text-blue-900">
                    ${results.totalInvestment.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    ₺{(results.totalInvestment * inputs.usdToLocalRate).toLocaleString()}
                  </div>
                </div>

                <div className="bg-green-50 rounded-lg p-4">
                  <div className="text-sm text-green-600 font-medium">Aylık Kar</div>
                  <div className="text-2xl font-bold text-green-900">
                    ${results.monthlyProfit.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    ₺{(results.monthlyProfit * inputs.usdToLocalRate).toLocaleString()}
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">Geri Ödeme Süresi</span>
                  <span className={`font-bold ${getPaybackColor(results.paybackPeriod)}`}>
                    {results.paybackPeriod.toFixed(1)} ay
                  </span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">12 Aylık ROI</span>
                  <span className={`font-bold ${getROIColor(results.roi12Months)}`}>
                    %{results.roi12Months.toFixed(1)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">24 Aylık ROI</span>
                  <span className={`font-bold ${getROIColor(results.roi24Months)}`}>
                    %{results.roi24Months.toFixed(1)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium text-gray-700">36 Aylık ROI</span>
                  <span className={`font-bold ${getROIColor(results.roi36Months)}`}>
                    %{results.roi36Months.toFixed(1)}
                  </span>
                </div>
              </div>
            </div>

            {/* Üretim ve Maliyet Detayları */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4 flex items-center">
                <Calculator className="w-5 h-5 mr-2 text-purple-600" />
                Üretim ve Maliyet Detayları
              </h2>
              
              <div className="space-y-3">
                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="text-sm text-blue-800 mb-2">Kozai Metodolojisi Sonuçları</div>
                  <div className="text-xs text-blue-700 space-y-1">
                    <div>• PPFD: 250-300 µmol/m²/s (Samsung LM301H EVO)</div>
                    <div>• Hasat döngüsü: {inputs.harvestCycle} gün</div>
                    <div>• Bitki yoğunluğu: {inputs.plantsPerM2} bitki/m²</div>
                    <div>• Toplam alan: {inputs.systemArea * inputs.layerCount}m² ({inputs.layerCount} kat)</div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Aylık Üretim</span>
                  <span className="font-medium">{results.monthlyProduction.toFixed(0)} kg</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Aylık Gelir</span>
                  <div className="text-right">
                    <span className="font-medium text-green-600">${results.monthlyRevenue.toLocaleString()}</span>
                    <div className="text-xs text-gray-500">₺{(results.monthlyRevenue * inputs.usdToLocalRate).toLocaleString()}</div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Aylık İşletme Maliyeti</span>
                  <div className="text-right">
                    <span className="font-medium text-red-600">${results.monthlyOperatingCost.toLocaleString()}</span>
                    <div className="text-xs text-gray-500">₺{(results.monthlyOperatingCost * inputs.usdToLocalRate).toLocaleString()}</div>
                  </div>
                </div>

                <hr className="my-2" />

                <div className="flex justify-between">
                  <span className="text-gray-600">Kg Başı Maliyet</span>
                  <div className="text-right">
                    <span className="font-medium">${results.costPerKg.toFixed(2)}</span>
                    <div className="text-xs text-gray-500">₺{(results.costPerKg * inputs.usdToLocalRate).toFixed(2)}</div>
                  </div>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Kar Marjı</span>
                  <span className="font-medium">{results.profitMargin.toFixed(1)}%</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Aylık Enerji Tüketimi</span>
                  <span className="font-medium">{results.monthlyEnergyConsumption.toFixed(0)} kWh</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-gray-600">Aylık Enerji Maliyeti</span>
                  <div className="text-right">
                    <span className="font-medium">${results.monthlyEnergyCost.toFixed(2)}</span>
                    <div className="text-xs text-gray-500">₺{(results.monthlyEnergyCost * inputs.usdToLocalRate).toFixed(2)}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
