{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/app/feasibility/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Calculator, TrendingUp, DollarSign, Zap, Calendar, Target, ArrowRight, Info } from 'lucide-react';\n\ninterface InvestmentInputs {\n  // Sistem Maliyetleri (USD)\n  ledModuleCost: number; // LED modül fiyatı (USD)\n  ledModuleCount: number; // LED modül sayısı\n  systemInfrastructure: number; // Sistem altyapısı (USD)\n  hydroponicEquipment: number; // Hidroponik ekipmanlar (USD)\n  installationCost: number; // Kurulum maliyeti (USD)\n\n  // İşletme Maliyetleri\n  electricityRate: number; // Elektrik fiyatı (USD cent/kWh)\n  seedCost: number; // Tohum maliyeti (USD/bitki)\n  nutrientCost: number; // Besin çözeltisi (USD/ay)\n  laborCost: number; // İşçilik (USD/ay)\n  maintenanceCost: number; // Bakım (USD/ay)\n\n  // Üretim Parametreleri\n  systemArea: number; // Sistem alanı (m²)\n  layerCount: number; // Kat sayısı\n  plantsPerM2: number; // m² başına bitki sayısı\n  harvestCycle: number; // Hasat döngüsü (gün)\n  yieldPerPlant: number; // Bitki başına verim (gram)\n\n  // Satış Parametreleri\n  sellingPrice: number; // Satış fiyatı (USD/kg)\n  marketLoss: number; // Pazar kaybı (%)\n\n  // Döviz Kuru\n  usdToLocalRate: number; // USD/TL kuru\n}\n\ninterface ROIResults {\n  // Yatırım\n  totalInvestment: number;\n  \n  // Aylık Değerler\n  monthlyProduction: number; // kg/ay\n  monthlyRevenue: number; // TL/ay\n  monthlyOperatingCost: number; // TL/ay\n  monthlyProfit: number; // TL/ay\n  \n  // ROI Metrikleri\n  paybackPeriod: number; // ay\n  roi12Months: number; // %\n  roi24Months: number; // %\n  roi36Months: number; // %\n  \n  // Birim Maliyetler\n  costPerKg: number; // TL/kg\n  profitMargin: number; // %\n  \n  // Enerji\n  monthlyEnergyConsumption: number; // kWh/ay\n  monthlyEnergyCost: number; // TL/ay\n}\n\nexport default function FeasibilityPage() {\n  const [inputs, setInputs] = useState<InvestmentInputs>({\n    // Sistem Maliyetleri (USD)\n    ledModuleCost: 28.5, // Samsung LM301H EVO modül fiyatı USD\n    ledModuleCount: 167, // Kozai hesaplamalarına göre 100m² için\n    systemInfrastructure: 2500, // Çelik konstrüksiyon, ventilasyon USD\n    hydroponicEquipment: 1500, // Tanklar, pompalar, sensörler USD\n    installationCost: 850, // Kurulum ve devreye alma USD\n\n    // İşletme Maliyetleri\n    electricityRate: 8.9, // USD cent/kWh (global ortalama)\n    seedCost: 0.005, // USD/bitki\n    nutrientCost: 85, // USD/ay\n    laborCost: 500, // USD/ay (2 kişi)\n    maintenanceCost: 100, // USD/ay\n\n    // Üretim Parametreleri\n    systemArea: 100, // m² (4 kat x 25 m²)\n    layerCount: 4,\n    plantsPerM2: 16, // 25cm x 25cm aralık (Kozai önerisi)\n    harvestCycle: 25, // gün (Kozai hızlı döngü)\n    yieldPerPlant: 180, // gram (ortalama marul ağırlığı)\n\n    // Satış Parametreleri\n    sellingPrice: 1.5, // USD/kg (organik marul)\n    marketLoss: 8, // % (nakliye, bozulma)\n\n    // Döviz Kuru\n    usdToLocalRate: 30 // USD/TL kuru\n  });\n\n  const [results, setResults] = useState<ROIResults>({\n    totalInvestment: 0,\n    monthlyProduction: 0,\n    monthlyRevenue: 0,\n    monthlyOperatingCost: 0,\n    monthlyProfit: 0,\n    paybackPeriod: 0,\n    roi12Months: 0,\n    roi24Months: 0,\n    roi36Months: 0,\n    costPerKg: 0,\n    profitMargin: 0,\n    monthlyEnergyConsumption: 0,\n    monthlyEnergyCost: 0\n  });\n\n  const [selectedScenario, setSelectedScenario] = useState<'conservative' | 'realistic' | 'optimistic'>('realistic');\n\n  // Samsung LM301H EVO LED modül hesaplamaları (Kozai metodolojisine göre)\n  const calculateLEDCoverage = (moduleCount: number) => {\n    // Her modül 0.6m² kaplar (Kozai'ye göre optimal PPFD için)\n    return moduleCount * 0.6;\n  };\n\n  const calculateModuleCount = (area: number) => {\n    // 0.6m² başına 1 modül (Kozai PPFD 250-300 µmol/m²/s için)\n    return Math.ceil(area / 0.6);\n  };\n\n  // Alan değiştiğinde modül sayısını güncelle\n  useEffect(() => {\n    const totalArea = inputs.systemArea * inputs.layerCount;\n    const requiredModules = calculateModuleCount(totalArea);\n    if (requiredModules !== inputs.ledModuleCount) {\n      setInputs(prev => ({ ...prev, ledModuleCount: requiredModules }));\n    }\n  }, [inputs.systemArea, inputs.layerCount]);\n\n  // Hesaplama fonksiyonu\n  useEffect(() => {\n    // Toplam yatırım (USD)\n    const totalInvestmentUSD =\n      inputs.ledModuleCost * inputs.ledModuleCount +\n      inputs.systemInfrastructure +\n      inputs.hydroponicEquipment +\n      inputs.installationCost;\n\n    // Aylık üretim hesaplama (Kozai parametrelerine göre)\n    const totalPlants = inputs.systemArea * inputs.layerCount * inputs.plantsPerM2;\n    const cyclesPerMonth = 30 / inputs.harvestCycle;\n    const monthlyPlantHarvest = totalPlants * cyclesPerMonth;\n    const monthlyProduction = (monthlyPlantHarvest * inputs.yieldPerPlant) / 1000; // kg\n    const marketableProduction = monthlyProduction * (1 - inputs.marketLoss / 100);\n\n    // Aylık gelir (USD)\n    const monthlyRevenue = marketableProduction * inputs.sellingPrice;\n\n    // Aylık enerji tüketimi ve maliyeti (Samsung LM301H EVO: 50W/modül)\n    const ledPowerConsumption = inputs.ledModuleCount * 50; // 50W per module\n    const dailyEnergyConsumption = (ledPowerConsumption * 14) / 1000; // 14 saat çalışma (Kozai önerisi), kWh\n    const monthlyEnergyConsumption = dailyEnergyConsumption * 30;\n    const monthlyEnergyCost = monthlyEnergyConsumption * (inputs.electricityRate / 100); // cent'ten USD'ye\n\n    // Aylık işletme maliyeti (USD)\n    const monthlySeedCost = monthlyPlantHarvest * inputs.seedCost;\n    const monthlyOperatingCost =\n      monthlyEnergyCost +\n      monthlySeedCost +\n      inputs.nutrientCost +\n      inputs.laborCost +\n      inputs.maintenanceCost;\n\n    // Aylık kar (USD)\n    const monthlyProfit = monthlyRevenue - monthlyOperatingCost;\n\n    // ROI hesaplamaları\n    const paybackPeriod = monthlyProfit > 0 ? totalInvestmentUSD / monthlyProfit : 0;\n    const roi12Months = ((monthlyProfit * 12 - totalInvestmentUSD) / totalInvestmentUSD) * 100;\n    const roi24Months = ((monthlyProfit * 24 - totalInvestmentUSD) / totalInvestmentUSD) * 100;\n    const roi36Months = ((monthlyProfit * 36 - totalInvestmentUSD) / totalInvestmentUSD) * 100;\n\n    // Birim maliyetler\n    const costPerKg = marketableProduction > 0 ? monthlyOperatingCost / marketableProduction : 0;\n    const profitMargin = inputs.sellingPrice > 0 ? ((inputs.sellingPrice - costPerKg) / inputs.sellingPrice) * 100 : 0;\n\n    setResults({\n      totalInvestment: totalInvestmentUSD,\n      monthlyProduction: marketableProduction,\n      monthlyRevenue,\n      monthlyOperatingCost,\n      monthlyProfit,\n      paybackPeriod,\n      roi12Months,\n      roi24Months,\n      roi36Months,\n      costPerKg,\n      profitMargin,\n      monthlyEnergyConsumption,\n      monthlyEnergyCost\n    });\n  }, [inputs]);\n\n  const applyScenario = (scenario: string) => {\n    const scenarios = {\n      conservative: {\n        sellingPrice: 1.2, // USD/kg\n        yieldPerPlant: 150, // gram\n        marketLoss: 12, // %\n        electricityRate: 12.5, // USD cent/kWh\n        harvestCycle: 28 // gün\n      },\n      realistic: {\n        sellingPrice: 1.5, // USD/kg\n        yieldPerPlant: 180, // gram\n        marketLoss: 8, // %\n        electricityRate: 8.9, // USD cent/kWh\n        harvestCycle: 25 // gün\n      },\n      optimistic: {\n        sellingPrice: 1.8, // USD/kg\n        yieldPerPlant: 220, // gram\n        marketLoss: 5, // %\n        electricityRate: 6.5, // USD cent/kWh\n        harvestCycle: 22 // gün\n      }\n    };\n\n    const scenarioData = scenarios[scenario as keyof typeof scenarios];\n    setInputs(prev => ({ ...prev, ...scenarioData }));\n    setSelectedScenario(scenario as any);\n  };\n\n  const getROIColor = (roi: number) => {\n    if (roi >= 50) return 'text-green-600';\n    if (roi >= 20) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  const getPaybackColor = (months: number) => {\n    if (months <= 18) return 'text-green-600';\n    if (months <= 30) return 'text-yellow-600';\n    return 'text-red-600';\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 py-8\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Header */}\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n            Hidroponik Sistem Fizibilite Analizi\n          </h1>\n          <p className=\"text-lg text-gray-600\">\n            Kozai metodolojisi ile 30 günlük döngülerde ROI hesaplama (USD bazlı)\n          </p>\n          <div className=\"mt-4 bg-blue-50 border border-blue-200 rounded-lg p-4 max-w-4xl mx-auto\">\n            <div className=\"flex items-start space-x-3\">\n              <Info className=\"w-5 h-5 text-blue-600 mt-0.5\" />\n              <div className=\"text-left\">\n                <h3 className=\"font-medium text-blue-900 mb-2\">Samsung LM301H EVO LED Hesaplamaları</h3>\n                <div className=\"text-sm text-blue-800 space-y-1\">\n                  <p>• Her modül 0.6m² alanı kaplar (Kozai PPFD 250-300 µmol/m²/s için optimal)</p>\n                  <p>• Güç tüketimi: 50W/modül, PPE: 2.975 µmol/J</p>\n                  <p>• Toplam alan: {inputs.systemArea * inputs.layerCount}m² = {inputs.ledModuleCount} modül gerekli</p>\n                  <p>• Fotoperiyot: 14 saat/gün (Kozai önerisi)</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Senaryo Seçimi */}\n        <div className=\"bg-white rounded-lg shadow-lg p-6 mb-8\">\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">Senaryo Seçimi</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {[\n              { key: 'conservative', name: 'Muhafazakar', desc: 'Düşük fiyat, yüksek maliyet', color: 'red' },\n              { key: 'realistic', name: 'Gerçekçi', desc: 'Mevcut piyasa koşulları', color: 'blue' },\n              { key: 'optimistic', name: 'İyimser', desc: 'Yüksek fiyat, düşük maliyet', color: 'green' }\n            ].map((scenario) => (\n              <button\n                key={scenario.key}\n                onClick={() => applyScenario(scenario.key)}\n                className={`p-4 rounded-lg border-2 transition-colors ${\n                  selectedScenario === scenario.key\n                    ? `border-${scenario.color}-500 bg-${scenario.color}-50`\n                    : 'border-gray-200 hover:border-gray-300'\n                }`}\n              >\n                <h3 className=\"font-medium text-gray-900\">{scenario.name}</h3>\n                <p className=\"text-sm text-gray-600 mt-1\">{scenario.desc}</p>\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n          {/* Giriş Parametreleri */}\n          <div className=\"space-y-6\">\n            {/* Yatırım Maliyetleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <DollarSign className=\"w-5 h-5 mr-2 text-blue-600\" />\n                Yatırım Maliyetleri (USD)\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      LED Modül Fiyatı (USD)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      value={inputs.ledModuleCost}\n                      onChange={(e) => setInputs(prev => ({ ...prev, ledModuleCost: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                    <div className=\"text-xs text-gray-500 mt-1\">Samsung LM301H EVO</div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      LED Modül Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.ledModuleCount}\n                      onChange={(e) => {\n                        const newCount = Number(e.target.value);\n                        const newArea = calculateLEDCoverage(newCount);\n                        setInputs(prev => ({\n                          ...prev,\n                          ledModuleCount: newCount,\n                          systemArea: Math.round(newArea / prev.layerCount)\n                        }));\n                      }}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      Kapladığı alan: {calculateLEDCoverage(inputs.ledModuleCount).toFixed(1)}m²\n                    </div>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Sistem Altyapısı (USD)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.systemInfrastructure}\n                    onChange={(e) => setInputs(prev => ({ ...prev, systemInfrastructure: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <div className=\"text-xs text-gray-500 mt-1\">Çelik konstrüksiyon, ventilasyon</div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Hidroponik Ekipmanlar (USD)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.hydroponicEquipment}\n                    onChange={(e) => setInputs(prev => ({ ...prev, hydroponicEquipment: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <div className=\"text-xs text-gray-500 mt-1\">Tanklar, pompalar, sensörler</div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Kurulum Maliyeti (USD)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.installationCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, installationCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <div className=\"text-xs text-gray-500 mt-1\">Devreye alma ve test</div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    USD/TL Kuru\n                  </label>\n                  <input\n                    type=\"number\"\n                    step=\"0.1\"\n                    value={inputs.usdToLocalRate}\n                    onChange={(e) => setInputs(prev => ({ ...prev, usdToLocalRate: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <div className=\"text-xs text-gray-500 mt-1\">\n                    Toplam yatırım: ₺{(results.totalInvestment * inputs.usdToLocalRate).toLocaleString()}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Üretim Parametreleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Target className=\"w-5 h-5 mr-2 text-green-600\" />\n                Üretim Parametreleri\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Sistem Alanı (m²)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.systemArea}\n                      onChange={(e) => setInputs(prev => ({ ...prev, systemArea: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Kat Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.layerCount}\n                      onChange={(e) => setInputs(prev => ({ ...prev, layerCount: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      m² Başına Bitki Sayısı\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.plantsPerM2}\n                      onChange={(e) => setInputs(prev => ({ ...prev, plantsPerM2: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Hasat Döngüsü (gün)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.harvestCycle}\n                      onChange={(e) => setInputs(prev => ({ ...prev, harvestCycle: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Bitki Başına Verim (gram)\n                    </label>\n                    <input\n                      type=\"number\"\n                      value={inputs.yieldPerPlant}\n                      onChange={(e) => setInputs(prev => ({ ...prev, yieldPerPlant: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Satış Fiyatı (USD/kg)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.1\"\n                      value={inputs.sellingPrice}\n                      onChange={(e) => setInputs(prev => ({ ...prev, sellingPrice: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                    <div className=\"text-xs text-gray-500 mt-1\">Organik marul pazar fiyatı</div>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Pazar Kaybı (%)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.marketLoss}\n                    onChange={(e) => setInputs(prev => ({ ...prev, marketLoss: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n\n            {/* İşletme Maliyetleri */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Zap className=\"w-5 h-5 mr-2 text-yellow-600\" />\n                İşletme Maliyetleri\n              </h2>\n              <div className=\"space-y-4\">\n                <div className=\"grid grid-cols-2 gap-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Elektrik Fiyatı (USD cent/kWh)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.01\"\n                      value={inputs.electricityRate}\n                      onChange={(e) => setInputs(prev => ({ ...prev, electricityRate: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                    <div className=\"text-xs text-gray-500 mt-1\">\n                      Aylık enerji maliyeti: ${results.monthlyEnergyCost.toFixed(2)}\n                    </div>\n                  </div>\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                      Tohum Maliyeti (USD/bitki)\n                    </label>\n                    <input\n                      type=\"number\"\n                      step=\"0.001\"\n                      value={inputs.seedCost}\n                      onChange={(e) => setInputs(prev => ({ ...prev, seedCost: Number(e.target.value) }))}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Besin Çözeltisi (USD/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.nutrientCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, nutrientCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    İşçilik Maliyeti (USD/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.laborCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, laborCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                  <div className=\"text-xs text-gray-500 mt-1\">2 kişi için aylık maaş</div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                    Bakım Maliyeti (USD/ay)\n                  </label>\n                  <input\n                    type=\"number\"\n                    value={inputs.maintenanceCost}\n                    onChange={(e) => setInputs(prev => ({ ...prev, maintenanceCost: Number(e.target.value) }))}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Sonuçlar */}\n          <div className=\"space-y-6\">\n            {/* ROI Özeti */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <TrendingUp className=\"w-5 h-5 mr-2 text-green-600\" />\n                ROI Analizi (USD)\n              </h2>\n\n              <div className=\"grid grid-cols-2 gap-4 mb-6\">\n                <div className=\"bg-blue-50 rounded-lg p-4\">\n                  <div className=\"text-sm text-blue-600 font-medium\">Toplam Yatırım</div>\n                  <div className=\"text-2xl font-bold text-blue-900\">\n                    ${results.totalInvestment.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    ₺{(results.totalInvestment * inputs.usdToLocalRate).toLocaleString()}\n                  </div>\n                </div>\n\n                <div className=\"bg-green-50 rounded-lg p-4\">\n                  <div className=\"text-sm text-green-600 font-medium\">Aylık Kar</div>\n                  <div className=\"text-2xl font-bold text-green-900\">\n                    ${results.monthlyProfit.toLocaleString()}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">\n                    ₺{(results.monthlyProfit * inputs.usdToLocalRate).toLocaleString()}\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">Geri Ödeme Süresi</span>\n                  <span className={`font-bold ${getPaybackColor(results.paybackPeriod)}`}>\n                    {results.paybackPeriod.toFixed(1)} ay\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">12 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi12Months)}`}>\n                    %{results.roi12Months.toFixed(1)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">24 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi24Months)}`}>\n                    %{results.roi24Months.toFixed(1)}\n                  </span>\n                </div>\n                \n                <div className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                  <span className=\"font-medium text-gray-700\">36 Aylık ROI</span>\n                  <span className={`font-bold ${getROIColor(results.roi36Months)}`}>\n                    %{results.roi36Months.toFixed(1)}\n                  </span>\n                </div>\n              </div>\n            </div>\n\n            {/* Üretim ve Maliyet Detayları */}\n            <div className=\"bg-white rounded-lg shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 mb-4 flex items-center\">\n                <Calculator className=\"w-5 h-5 mr-2 text-purple-600\" />\n                Üretim ve Maliyet Detayları\n              </h2>\n              \n              <div className=\"space-y-3\">\n                <div className=\"bg-blue-50 p-3 rounded-lg\">\n                  <div className=\"text-sm text-blue-800 mb-2\">Kozai Metodolojisi Sonuçları</div>\n                  <div className=\"text-xs text-blue-700 space-y-1\">\n                    <div>• PPFD: 250-300 µmol/m²/s (Samsung LM301H EVO)</div>\n                    <div>• Hasat döngüsü: {inputs.harvestCycle} gün</div>\n                    <div>• Bitki yoğunluğu: {inputs.plantsPerM2} bitki/m²</div>\n                    <div>• Toplam alan: {inputs.systemArea * inputs.layerCount}m² ({inputs.layerCount} kat)</div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Üretim</span>\n                  <span className=\"font-medium\">{results.monthlyProduction.toFixed(0)} kg</span>\n                </div>\n                \n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Gelir</span>\n                  <div className=\"text-right\">\n                    <span className=\"font-medium text-green-600\">${results.monthlyRevenue.toLocaleString()}</span>\n                    <div className=\"text-xs text-gray-500\">₺{(results.monthlyRevenue * inputs.usdToLocalRate).toLocaleString()}</div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık İşletme Maliyeti</span>\n                  <div className=\"text-right\">\n                    <span className=\"font-medium text-red-600\">${results.monthlyOperatingCost.toLocaleString()}</span>\n                    <div className=\"text-xs text-gray-500\">₺{(results.monthlyOperatingCost * inputs.usdToLocalRate).toLocaleString()}</div>\n                  </div>\n                </div>\n\n                <hr className=\"my-2\" />\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Kg Başı Maliyet</span>\n                  <div className=\"text-right\">\n                    <span className=\"font-medium\">${results.costPerKg.toFixed(2)}</span>\n                    <div className=\"text-xs text-gray-500\">₺{(results.costPerKg * inputs.usdToLocalRate).toFixed(2)}</div>\n                  </div>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Kar Marjı</span>\n                  <span className=\"font-medium\">{results.profitMargin.toFixed(1)}%</span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Enerji Tüketimi</span>\n                  <span className=\"font-medium\">{results.monthlyEnergyConsumption.toFixed(0)} kWh</span>\n                </div>\n\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-600\">Aylık Enerji Maliyeti</span>\n                  <div className=\"text-right\">\n                    <span className=\"font-medium\">${results.monthlyEnergyCost.toFixed(2)}</span>\n                    <div className=\"text-xs text-gray-500\">₺{(results.monthlyEnergyCost * inputs.usdToLocalRate).toFixed(2)}</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AA4De,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACrD,2BAA2B;QAC3B,eAAe;QACf,gBAAgB;QAChB,sBAAsB;QACtB,qBAAqB;QACrB,kBAAkB;QAElB,sBAAsB;QACtB,iBAAiB;QACjB,UAAU;QACV,cAAc;QACd,WAAW;QACX,iBAAiB;QAEjB,uBAAuB;QACvB,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,cAAc;QACd,eAAe;QAEf,sBAAsB;QACtB,cAAc;QACd,YAAY;QAEZ,aAAa;QACb,gBAAgB,GAAG,cAAc;IACnC;IAEA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACjD,iBAAiB;QACjB,mBAAmB;QACnB,gBAAgB;QAChB,sBAAsB;QACtB,eAAe;QACf,eAAe;QACf,aAAa;QACb,aAAa;QACb,aAAa;QACb,WAAW;QACX,cAAc;QACd,0BAA0B;QAC1B,mBAAmB;IACrB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA+C;IAEtG,yEAAyE;IACzE,MAAM,uBAAuB,CAAC;QAC5B,2DAA2D;QAC3D,OAAO,cAAc;IACvB;IAEA,MAAM,uBAAuB,CAAC;QAC5B,2DAA2D;QAC3D,OAAO,KAAK,IAAI,CAAC,OAAO;IAC1B;IAEA,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,YAAY,OAAO,UAAU,GAAG,OAAO,UAAU;YACvD,MAAM,kBAAkB,qBAAqB;YAC7C,IAAI,oBAAoB,OAAO,cAAc,EAAE;gBAC7C;iDAAU,CAAA,OAAQ,CAAC;4BAAE,GAAG,IAAI;4BAAE,gBAAgB;wBAAgB,CAAC;;YACjE;QACF;oCAAG;QAAC,OAAO,UAAU;QAAE,OAAO,UAAU;KAAC;IAEzC,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,uBAAuB;YACvB,MAAM,qBACJ,OAAO,aAAa,GAAG,OAAO,cAAc,GAC5C,OAAO,oBAAoB,GAC3B,OAAO,mBAAmB,GAC1B,OAAO,gBAAgB;YAEzB,sDAAsD;YACtD,MAAM,cAAc,OAAO,UAAU,GAAG,OAAO,UAAU,GAAG,OAAO,WAAW;YAC9E,MAAM,iBAAiB,KAAK,OAAO,YAAY;YAC/C,MAAM,sBAAsB,cAAc;YAC1C,MAAM,oBAAoB,AAAC,sBAAsB,OAAO,aAAa,GAAI,MAAM,KAAK;YACpF,MAAM,uBAAuB,oBAAoB,CAAC,IAAI,OAAO,UAAU,GAAG,GAAG;YAE7E,oBAAoB;YACpB,MAAM,iBAAiB,uBAAuB,OAAO,YAAY;YAEjE,oEAAoE;YACpE,MAAM,sBAAsB,OAAO,cAAc,GAAG,IAAI,iBAAiB;YACzE,MAAM,yBAAyB,AAAC,sBAAsB,KAAM,MAAM,uCAAuC;YACzG,MAAM,2BAA2B,yBAAyB;YAC1D,MAAM,oBAAoB,2BAA2B,CAAC,OAAO,eAAe,GAAG,GAAG,GAAG,kBAAkB;YAEvG,+BAA+B;YAC/B,MAAM,kBAAkB,sBAAsB,OAAO,QAAQ;YAC7D,MAAM,uBACJ,oBACA,kBACA,OAAO,YAAY,GACnB,OAAO,SAAS,GAChB,OAAO,eAAe;YAExB,kBAAkB;YAClB,MAAM,gBAAgB,iBAAiB;YAEvC,oBAAoB;YACpB,MAAM,gBAAgB,gBAAgB,IAAI,qBAAqB,gBAAgB;YAC/E,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,kBAAkB,IAAI,qBAAsB;YACvF,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,kBAAkB,IAAI,qBAAsB;YACvF,MAAM,cAAc,AAAC,CAAC,gBAAgB,KAAK,kBAAkB,IAAI,qBAAsB;YAEvF,mBAAmB;YACnB,MAAM,YAAY,uBAAuB,IAAI,uBAAuB,uBAAuB;YAC3F,MAAM,eAAe,OAAO,YAAY,GAAG,IAAI,AAAC,CAAC,OAAO,YAAY,GAAG,SAAS,IAAI,OAAO,YAAY,GAAI,MAAM;YAEjH,WAAW;gBACT,iBAAiB;gBACjB,mBAAmB;gBACnB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;oCAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;YAChB,cAAc;gBACZ,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;YACA,WAAW;gBACT,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;YACA,YAAY;gBACV,cAAc;gBACd,eAAe;gBACf,YAAY;gBACZ,iBAAiB;gBACjB,cAAc,GAAG,MAAM;YACzB;QACF;QAEA,MAAM,eAAe,SAAS,CAAC,SAAmC;QAClE,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,YAAY;YAAC,CAAC;QAC/C,oBAAoB;IACtB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,UAAU,IAAI,OAAO;QACzB,IAAI,UAAU,IAAI,OAAO;QACzB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;sCAGrC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;;4DAAE;4DAAgB,OAAO,UAAU,GAAG,OAAO,UAAU;4DAAC;4DAAM,OAAO,cAAc;4DAAC;;;;;;;kEACrF,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,KAAK;oCAAgB,MAAM;oCAAe,MAAM;oCAA+B,OAAO;gCAAM;gCAC9F;oCAAE,KAAK;oCAAa,MAAM;oCAAY,MAAM;oCAA2B,OAAO;gCAAO;gCACrF;oCAAE,KAAK;oCAAc,MAAM;oCAAW,MAAM;oCAA+B,OAAO;gCAAQ;6BAC3F,CAAC,GAAG,CAAC,CAAC,yBACL,6LAAC;oCAEC,SAAS,IAAM,cAAc,SAAS,GAAG;oCACzC,WAAW,AAAC,6CAIX,OAHC,qBAAqB,SAAS,GAAG,GAC7B,AAAC,UAAkC,OAAzB,SAAS,KAAK,EAAC,YAAyB,OAAf,SAAS,KAAK,EAAC,SAClD;;sDAGN,6LAAC;4CAAG,WAAU;sDAA6B,SAAS,IAAI;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAA8B,SAAS,IAAI;;;;;;;mCATnD,SAAS,GAAG;;;;;;;;;;;;;;;;8BAezB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAA+B;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,aAAa;oEAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACtF,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;;;;;;;sEAE9C,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,cAAc;oEAC5B,UAAU,CAAC;wEACT,MAAM,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;wEACtC,MAAM,UAAU,qBAAqB;wEACrC,UAAU,CAAA,OAAQ,CAAC;gFACjB,GAAG,IAAI;gFACP,gBAAgB;gFAChB,YAAY,KAAK,KAAK,CAAC,UAAU,KAAK,UAAU;4EAClD,CAAC;oEACH;oEACA,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;wEAA6B;wEACzB,qBAAqB,OAAO,cAAc,EAAE,OAAO,CAAC;wEAAG;;;;;;;;;;;;;;;;;;;8DAK9E,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,oBAAoB;4DAClC,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAC7F,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAG9C,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,mBAAmB;4DACjC,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAC5F,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAG9C,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,gBAAgB;4DAC9B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACzF,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAG9C,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,MAAK;4DACL,OAAO,OAAO,cAAc;4DAC5B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACvF,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;;gEAA6B;gEACxB,CAAC,QAAQ,eAAe,GAAG,OAAO,cAAc,EAAE,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;8CAO1F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAGpD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACnF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,UAAU;oEACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACnF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,WAAW;oEACzB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,aAAa,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACpF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,YAAY;oEAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACrF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,OAAO,OAAO,aAAa;oEAC3B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,eAAe,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACtF,WAAU;;;;;;;;;;;;sEAGd,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,YAAY;oEAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACrF,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EAA6B;;;;;;;;;;;;;;;;;;8DAIhD,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,UAAU;4DACxB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;8CAOlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAGlD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,eAAe;oEAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACxF,WAAU;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;;wEAA6B;wEACjB,QAAQ,iBAAiB,CAAC,OAAO,CAAC;;;;;;;;;;;;;sEAG/D,6LAAC;;8EACC,6LAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6LAAC;oEACC,MAAK;oEACL,MAAK;oEACL,OAAO,OAAO,QAAQ;oEACtB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;gFAAE,GAAG,IAAI;gFAAE,UAAU,OAAO,EAAE,MAAM,CAAC,KAAK;4EAAE,CAAC;oEACjF,WAAU;;;;;;;;;;;;;;;;;;8DAKhB,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,YAAY;4DAC1B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACrF,WAAU;;;;;;;;;;;;8DAId,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,SAAS;4DACvB,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DAClF,WAAU;;;;;;sEAEZ,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;8DAG9C,6LAAC;;sEACC,6LAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6LAAC;4DACC,MAAK;4DACL,OAAO,OAAO,eAAe;4DAC7B,UAAU,CAAC,IAAM,UAAU,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAE,CAAC;4DACxF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQpB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAgC;;;;;;;sDAIxD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAoC;;;;;;sEACnD,6LAAC;4DAAI,WAAU;;gEAAmC;gEAC9C,QAAQ,eAAe,CAAC,cAAc;;;;;;;sEAE1C,6LAAC;4DAAI,WAAU;;gEAAwB;gEACnC,CAAC,QAAQ,eAAe,GAAG,OAAO,cAAc,EAAE,cAAc;;;;;;;;;;;;;8DAItE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAqC;;;;;;sEACpD,6LAAC;4DAAI,WAAU;;gEAAoC;gEAC/C,QAAQ,aAAa,CAAC,cAAc;;;;;;;sEAExC,6LAAC;4DAAI,WAAU;;gEAAwB;gEACnC,CAAC,QAAQ,aAAa,GAAG,OAAO,cAAc,EAAE,cAAc;;;;;;;;;;;;;;;;;;;sDAKtE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAAmD,OAAvC,gBAAgB,QAAQ,aAAa;;gEAChE,QAAQ,aAAa,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAItC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAIlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;8DAIlC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAA4B;;;;;;sEAC5C,6LAAC;4DAAK,WAAW,AAAC,aAA6C,OAAjC,YAAY,QAAQ,WAAW;;gEAAK;gEAC9D,QAAQ,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC,iNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;gDAAiC;;;;;;;sDAIzD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;sEAC5C,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;8EAAI;;;;;;8EACL,6LAAC;;wEAAI;wEAAkB,OAAO,YAAY;wEAAC;;;;;;;8EAC3C,6LAAC;;wEAAI;wEAAoB,OAAO,WAAW;wEAAC;;;;;;;8EAC5C,6LAAC;;wEAAI;wEAAgB,OAAO,UAAU,GAAG,OAAO,UAAU;wEAAC;wEAAK,OAAO,UAAU;wEAAC;;;;;;;;;;;;;;;;;;;8DAItF,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,iBAAiB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGtE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAA6B;wEAAE,QAAQ,cAAc,CAAC,cAAc;;;;;;;8EACpF,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,CAAC,QAAQ,cAAc,GAAG,OAAO,cAAc,EAAE,cAAc;;;;;;;;;;;;;;;;;;;8DAI5G,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAA2B;wEAAE,QAAQ,oBAAoB,CAAC,cAAc;;;;;;;8EACxF,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,CAAC,QAAQ,oBAAoB,GAAG,OAAO,cAAc,EAAE,cAAc;;;;;;;;;;;;;;;;;;;8DAIlH,6LAAC;oDAAG,WAAU;;;;;;8DAEd,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAc;wEAAE,QAAQ,SAAS,CAAC,OAAO,CAAC;;;;;;;8EAC1D,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,CAAC,QAAQ,SAAS,GAAG,OAAO,cAAc,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;8DAIjG,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,YAAY,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAGjE,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,QAAQ,wBAAwB,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAG7E,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;;wEAAc;wEAAE,QAAQ,iBAAiB,CAAC,OAAO,CAAC;;;;;;;8EAClE,6LAAC;oEAAI,WAAU;;wEAAwB;wEAAE,CAAC,QAAQ,iBAAiB,GAAG,OAAO,cAAc,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUzH;GAloBwB;KAAA", "debugId": null}}]}