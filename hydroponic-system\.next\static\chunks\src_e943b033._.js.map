{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/Dashboard.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Thermometer, Droplets, Lightbulb, Zap, AlertTriangle, CheckCircle } from 'lucide-react';\n\ninterface SensorReading {\n  temperature: number;\n  humidity: number;\n  ph: number;\n  ec: number;\n  ppfd: number;\n  dli: number;\n  co2: number;\n}\n\nexport default function Dashboard() {\n  const [sensorData, setSensorData] = useState<SensorReading>({\n    temperature: 22.5,\n    humidity: 65,\n    ph: 6.1,\n    ec: 1.6,\n    ppfd: 275,\n    dli: 14.2,\n    co2: 850\n  });\n\n  const [alerts, setAlerts] = useState([\n    { id: 1, type: 'warning', message: 'PPFD değeri optimal aralığın üstünde', time: '2 dakika önce' },\n    { id: 2, type: 'info', message: 'LED dimmer %85 seviyesinde', time: '5 dakika önce' }\n  ]);\n\n  // Kozai parametrelerine göre durum kontrolü\n  const getStatus = (value: number, min: number, max: number) => {\n    if (value >= min && value <= max) return 'optimal';\n    if (value < min * 0.9 || value > max * 1.1) return 'critical';\n    return 'warning';\n  };\n\n  const parameters = [\n    {\n      name: 'Sıcaklık',\n      value: sensorData.temperature,\n      unit: '°C',\n      min: 20,\n      max: 24,\n      icon: Thermometer,\n      color: 'blue'\n    },\n    {\n      name: 'Nem',\n      value: sensorData.humidity,\n      unit: '%',\n      min: 60,\n      max: 70,\n      icon: Droplets,\n      color: 'cyan'\n    },\n    {\n      name: 'pH',\n      value: sensorData.ph,\n      unit: '',\n      min: 5.8,\n      max: 6.3,\n      icon: Droplets,\n      color: 'purple'\n    },\n    {\n      name: 'EC',\n      value: sensorData.ec,\n      unit: 'mS/cm',\n      min: 1.4,\n      max: 1.8,\n      icon: Zap,\n      color: 'yellow'\n    },\n    {\n      name: 'PPFD',\n      value: sensorData.ppfd,\n      unit: 'µmol/m²/s',\n      min: 200,\n      max: 300,\n      icon: Lightbulb,\n      color: 'orange'\n    },\n    {\n      name: 'DLI',\n      value: sensorData.dli,\n      unit: 'mol/m²/gün',\n      min: 12,\n      max: 17,\n      icon: Lightbulb,\n      color: 'green'\n    }\n  ];\n\n  useEffect(() => {\n    // Simüle edilmiş gerçek zamanlı veri güncellemesi\n    const interval = setInterval(() => {\n      setSensorData(prev => ({\n        ...prev,\n        temperature: prev.temperature + (Math.random() - 0.5) * 0.2,\n        humidity: prev.humidity + (Math.random() - 0.5) * 2,\n        ppfd: prev.ppfd + (Math.random() - 0.5) * 10,\n        dli: prev.dli + (Math.random() - 0.5) * 0.5\n      }));\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Sistem Durumu */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Sistem Durumu</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n          {parameters.map((param) => {\n            const Icon = param.icon;\n            const status = getStatus(param.value, param.min, param.max);\n            \n            return (\n              <div key={param.name} className=\"bg-gray-50 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <Icon className={`w-5 h-5 text-${param.color}-500`} />\n                    <span className=\"text-sm font-medium text-gray-700\">{param.name}</span>\n                  </div>\n                  <div className={`w-3 h-3 rounded-full ${\n                    status === 'optimal' ? 'bg-green-500' :\n                    status === 'warning' ? 'bg-yellow-500' : 'bg-red-500'\n                  }`}></div>\n                </div>\n                <div className=\"text-2xl font-bold text-gray-900\">\n                  {param.value.toFixed(1)}{param.unit}\n                </div>\n                <div className=\"text-xs text-gray-500 mt-1\">\n                  Optimal: {param.min}-{param.max}{param.unit}\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Uyarılar */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Sistem Uyarıları</h2>\n        <div className=\"space-y-3\">\n          {alerts.map((alert) => (\n            <div key={alert.id} className={`flex items-start space-x-3 p-3 rounded-lg ${\n              alert.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' :\n              alert.type === 'error' ? 'bg-red-50 border border-red-200' :\n              'bg-blue-50 border border-blue-200'\n            }`}>\n              {alert.type === 'warning' ? (\n                <AlertTriangle className=\"w-5 h-5 text-yellow-500 mt-0.5\" />\n              ) : alert.type === 'error' ? (\n                <AlertTriangle className=\"w-5 h-5 text-red-500 mt-0.5\" />\n              ) : (\n                <CheckCircle className=\"w-5 h-5 text-blue-500 mt-0.5\" />\n              )}\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-gray-900\">{alert.message}</p>\n                <p className=\"text-xs text-gray-500\">{alert.time}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Hızlı İstatistikler */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Günlük Enerji Tüketimi</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">117.8 kWh</p>\n          <p className=\"text-sm text-green-600\">%5 azalma</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Aktif Bitki Sayısı</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">1,247</p>\n          <p className=\"text-sm text-blue-600\">4 katlı sistem</p>\n        </div>\n        \n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <h3 className=\"text-sm font-medium text-gray-500 mb-2\">Tahmini Hasat</h3>\n          <p className=\"text-2xl font-bold text-gray-900\">18 gün</p>\n          <p className=\"text-sm text-purple-600\">Marul Rex çeşidi</p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAee,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QAC1D,aAAa;QACb,UAAU;QACV,IAAI;QACJ,IAAI;QACJ,MAAM;QACN,KAAK;QACL,KAAK;IACP;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnC;YAAE,IAAI;YAAG,MAAM;YAAW,SAAS;YAAwC,MAAM;QAAgB;QACjG;YAAE,IAAI;YAAG,MAAM;YAAQ,SAAS;YAA8B,MAAM;QAAgB;KACrF;IAED,4CAA4C;IAC5C,MAAM,YAAY,CAAC,OAAe,KAAa;QAC7C,IAAI,SAAS,OAAO,SAAS,KAAK,OAAO;QACzC,IAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,KAAK,OAAO;QACnD,OAAO;IACT;IAEA,MAAM,aAAa;QACjB;YACE,MAAM;YACN,OAAO,WAAW,WAAW;YAC7B,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,mNAAA,CAAA,cAAW;YACjB,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,WAAW,QAAQ;YAC1B,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,WAAW,EAAE;YACpB,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,6MAAA,CAAA,WAAQ;YACd,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,WAAW,EAAE;YACpB,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,mMAAA,CAAA,MAAG;YACT,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,WAAW,IAAI;YACtB,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,+MAAA,CAAA,YAAS;YACf,OAAO;QACT;QACA;YACE,MAAM;YACN,OAAO,WAAW,GAAG;YACrB,MAAM;YACN,KAAK;YACL,KAAK;YACL,MAAM,+MAAA,CAAA,YAAS;YACf,OAAO;QACT;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,kDAAkD;YAClD,MAAM,WAAW;gDAAY;oBAC3B;wDAAc,CAAA,OAAQ,CAAC;gCACrB,GAAG,IAAI;gCACP,aAAa,KAAK,WAAW,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxD,UAAU,KAAK,QAAQ,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAClD,MAAM,KAAK,IAAI,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAC1C,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC1C,CAAC;;gBACH;+CAAG;YAEH;uCAAO,IAAM,cAAc;;QAC7B;8BAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,OAAO,MAAM,IAAI;4BACvB,MAAM,SAAS,UAAU,MAAM,KAAK,EAAE,MAAM,GAAG,EAAE,MAAM,GAAG;4BAE1D,qBACE,6LAAC;gCAAqB,WAAU;;kDAC9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,AAAC,gBAA2B,OAAZ,MAAM,KAAK,EAAC;;;;;;kEAC7C,6LAAC;wDAAK,WAAU;kEAAqC,MAAM,IAAI;;;;;;;;;;;;0DAEjE,6LAAC;gDAAI,WAAW,AAAC,wBAGhB,OAFC,WAAW,YAAY,iBACvB,WAAW,YAAY,kBAAkB;;;;;;;;;;;;kDAG7C,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,KAAK,CAAC,OAAO,CAAC;4CAAI,MAAM,IAAI;;;;;;;kDAErC,6LAAC;wCAAI,WAAU;;4CAA6B;4CAChC,MAAM,GAAG;4CAAC;4CAAE,MAAM,GAAG;4CAAE,MAAM,IAAI;;;;;;;;+BAfrC,MAAM,IAAI;;;;;wBAmBxB;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC;gCAAmB,WAAW,AAAC,6CAI/B,OAHC,MAAM,IAAI,KAAK,YAAY,0CAC3B,MAAM,IAAI,KAAK,UAAU,oCACzB;;oCAEC,MAAM,IAAI,KAAK,0BACd,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;+CACvB,MAAM,IAAI,KAAK,wBACjB,6LAAC,2NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;6DAEzB,6LAAC,8NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDAEzB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;0DAAqC,MAAM,OAAO;;;;;;0DAC/D,6LAAC;gDAAE,WAAU;0DAAyB,MAAM,IAAI;;;;;;;;;;;;;+BAd1C,MAAM,EAAE;;;;;;;;;;;;;;;;0BAsBxB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAAyB;;;;;;;;;;;;kCAGxC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAGvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,6LAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,6LAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;;;;;;;;;;;;;AAKjD;GAjLwB;KAAA", "debugId": null}}, {"offset": {"line": 441, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/LEDControl.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Lightbulb, Sun, Moon, Setting<PERSON>, Power } from 'lucide-react';\n\ninterface LEDSettings {\n  power: boolean;\n  intensity: number;\n  distance: number;\n  photoperiod: number;\n  schedule: {\n    start: string;\n    end: string;\n  };\n}\n\nexport default function LEDControl() {\n  const [ledSettings, setLedSettings] = useState<LEDSettings>({\n    power: true,\n    intensity: 75,\n    distance: 35,\n    photoperiod: 14,\n    schedule: {\n      start: '06:00',\n      end: '20:00'\n    }\n  });\n\n  const [autoMode, setAutoMode] = useState(true);\n\n  // PPFD hesaplama (Samsung LM301H EVO için yaklaşık)\n  const calculatePPFD = (intensity: number, distance: number) => {\n    const basePPFD = 400; // 30cm mesafede %100 güçte\n    const distanceFactor = Math.pow(30 / distance, 2);\n    const intensityFactor = intensity / 100;\n    return Math.round(basePPFD * distanceFactor * intensityFactor);\n  };\n\n  // DLI hesaplama\n  const calculateDLI = (ppfd: number, hours: number) => {\n    return ((ppfd * hours * 3600) / 1000000).toFixed(1);\n  };\n\n  const currentPPFD = calculatePPFD(ledSettings.intensity, ledSettings.distance);\n  const currentDLI = calculateDLI(currentPPFD, ledSettings.photoperiod);\n\n  const handleIntensityChange = (value: number) => {\n    setLedSettings(prev => ({ ...prev, intensity: value }));\n  };\n\n  const handleDistanceChange = (value: number) => {\n    setLedSettings(prev => ({ ...prev, distance: value }));\n  };\n\n  const handlePhotoperiodChange = (value: number) => {\n    setLedSettings(prev => ({ ...prev, photoperiod: value }));\n  };\n\n  const getStatusColor = (ppfd: number) => {\n    if (ppfd >= 200 && ppfd <= 300) return 'text-green-600';\n    if (ppfd > 350) return 'text-red-600';\n    return 'text-yellow-600';\n  };\n\n  const getRecommendation = (ppfd: number) => {\n    if (ppfd > 350) return 'PPFD çok yüksek! LED mesafesini artırın veya gücü azaltın.';\n    if (ppfd < 200) return 'PPFD düşük. LED mesafesini azaltın veya gücü artırın.';\n    if (ppfd >= 200 && ppfd <= 300) return 'Optimal PPFD aralığında. Kozai parametrelerine uygun.';\n    return 'PPFD kabul edilebilir aralıkta.';\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* LED Durum Kartı */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">LED Işık Kontrolü</h2>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">Otomatik Mod</span>\n              <button\n                onClick={() => setAutoMode(!autoMode)}\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                  autoMode ? 'bg-green-600' : 'bg-gray-200'\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    autoMode ? 'translate-x-6' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n            </div>\n            <button\n              onClick={() => setLedSettings(prev => ({ ...prev, power: !prev.power }))}\n              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium ${\n                ledSettings.power\n                  ? 'bg-green-100 text-green-700 hover:bg-green-200'\n                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'\n              }`}\n            >\n              <Power className=\"w-4 h-4\" />\n              <span>{ledSettings.power ? 'Açık' : 'Kapalı'}</span>\n            </button>\n          </div>\n        </div>\n\n        {/* Mevcut Değerler */}\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6 mb-6\">\n          <div className=\"bg-blue-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Lightbulb className=\"w-5 h-5 text-blue-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">PPFD</span>\n            </div>\n            <div className={`text-2xl font-bold ${getStatusColor(currentPPFD)}`}>\n              {currentPPFD} µmol/m²/s\n            </div>\n            <div className=\"text-xs text-gray-500 mt-1\">Hedef: 200-300</div>\n          </div>\n\n          <div className=\"bg-green-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Sun className=\"w-5 h-5 text-green-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">DLI</span>\n            </div>\n            <div className=\"text-2xl font-bold text-green-600\">\n              {currentDLI} mol/m²/gün\n            </div>\n            <div className=\"text-xs text-gray-500 mt-1\">Hedef: 12-17</div>\n          </div>\n\n          <div className=\"bg-purple-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2 mb-2\">\n              <Settings className=\"w-5 h-5 text-purple-500\" />\n              <span className=\"text-sm font-medium text-gray-700\">Güç Tüketimi</span>\n            </div>\n            <div className=\"text-2xl font-bold text-purple-600\">\n              {((ledSettings.intensity / 100) * 8.4).toFixed(1)} kW\n            </div>\n            <div className=\"text-xs text-gray-500 mt-1\">Maksimum: 8.4 kW</div>\n          </div>\n        </div>\n\n        {/* Öneri */}\n        <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\">\n          <p className=\"text-sm text-yellow-800\">{getRecommendation(currentPPFD)}</p>\n        </div>\n      </div>\n\n      {/* Kontrol Paneli */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-6\">Manuel Kontroller</h3>\n        \n        <div className=\"space-y-6\">\n          {/* LED Yoğunluğu */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              LED Yoğunluğu: {ledSettings.intensity}%\n            </label>\n            <input\n              type=\"range\"\n              min=\"0\"\n              max=\"100\"\n              value={ledSettings.intensity}\n              onChange={(e) => handleIntensityChange(Number(e.target.value))}\n              disabled={autoMode}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50\"\n            />\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>0%</span>\n              <span>50%</span>\n              <span>100%</span>\n            </div>\n          </div>\n\n          {/* LED Mesafesi */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              LED Mesafesi: {ledSettings.distance} cm\n            </label>\n            <input\n              type=\"range\"\n              min=\"20\"\n              max=\"50\"\n              value={ledSettings.distance}\n              onChange={(e) => handleDistanceChange(Number(e.target.value))}\n              disabled={autoMode}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50\"\n            />\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>20 cm</span>\n              <span>35 cm</span>\n              <span>50 cm</span>\n            </div>\n          </div>\n\n          {/* Fotoperiyot */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Fotoperiyot: {ledSettings.photoperiod} saat\n            </label>\n            <input\n              type=\"range\"\n              min=\"8\"\n              max=\"18\"\n              value={ledSettings.photoperiod}\n              onChange={(e) => handlePhotoperiodChange(Number(e.target.value))}\n              disabled={autoMode}\n              className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50\"\n            />\n            <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n              <span>8h</span>\n              <span>14h</span>\n              <span>18h</span>\n            </div>\n          </div>\n\n          {/* Zamanlama */}\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Başlangıç Saati\n              </label>\n              <input\n                type=\"time\"\n                value={ledSettings.schedule.start}\n                onChange={(e) => setLedSettings(prev => ({\n                  ...prev,\n                  schedule: { ...prev.schedule, start: e.target.value }\n                }))}\n                disabled={autoMode}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50\"\n              />\n            </div>\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Bitiş Saati\n              </label>\n              <input\n                type=\"time\"\n                value={ledSettings.schedule.end}\n                onChange={(e) => setLedSettings(prev => ({\n                  ...prev,\n                  schedule: { ...prev.schedule, end: e.target.value }\n                }))}\n                disabled={autoMode}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Kozai Önerileri */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Kozai Önerileri</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"bg-green-50 rounded-lg p-4\">\n            <h4 className=\"font-medium text-green-800 mb-2\">Optimal Ayarlar</h4>\n            <ul className=\"text-sm text-green-700 space-y-1\">\n              <li>• PPFD: 250-300 µmol/m²/s</li>\n              <li>• DLI: 12-15 mol/m²/gün</li>\n              <li>• Fotoperiyot: 14-16 saat</li>\n              <li>• LED mesafesi: 35-40 cm</li>\n            </ul>\n          </div>\n          <div className=\"bg-blue-50 rounded-lg p-4\">\n            <h4 className=\"font-medium text-blue-800 mb-2\">Enerji Tasarrufu</h4>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• PPFD'yi 200-220'ye düşür</li>\n              <li>• Fotoperiyodu 12 saate indir</li>\n              <li>• %60-80 güç aralığı kullan</li>\n              <li>• CO₂ yoksa ışığı kıs</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAgBe,SAAS;;IACtB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;QAC1D,OAAO;QACP,WAAW;QACX,UAAU;QACV,aAAa;QACb,UAAU;YACR,OAAO;YACP,KAAK;QACP;IACF;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,oDAAoD;IACpD,MAAM,gBAAgB,CAAC,WAAmB;QACxC,MAAM,WAAW,KAAK,2BAA2B;QACjD,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,UAAU;QAC/C,MAAM,kBAAkB,YAAY;QACpC,OAAO,KAAK,KAAK,CAAC,WAAW,iBAAiB;IAChD;IAEA,gBAAgB;IAChB,MAAM,eAAe,CAAC,MAAc;QAClC,OAAO,CAAC,AAAC,OAAO,QAAQ,OAAQ,OAAO,EAAE,OAAO,CAAC;IACnD;IAEA,MAAM,cAAc,cAAc,YAAY,SAAS,EAAE,YAAY,QAAQ;IAC7E,MAAM,aAAa,aAAa,aAAa,YAAY,WAAW;IAEpE,MAAM,wBAAwB,CAAC;QAC7B,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,WAAW;YAAM,CAAC;IACvD;IAEA,MAAM,uBAAuB,CAAC;QAC5B,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAM,CAAC;IACtD;IAEA,MAAM,0BAA0B,CAAC;QAC/B,eAAe,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,aAAa;YAAM,CAAC;IACzD;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO;QACvC,IAAI,OAAO,KAAK,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,OAAO,KAAK,OAAO;QACvB,IAAI,QAAQ,OAAO,QAAQ,KAAK,OAAO;QACvC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDACC,SAAS,IAAM,YAAY,CAAC;gDAC5B,WAAW,AAAC,6EAEX,OADC,WAAW,iBAAiB;0DAG9B,cAAA,6LAAC;oDACC,WAAW,AAAC,6EAEX,OADC,WAAW,kBAAkB;;;;;;;;;;;;;;;;;kDAKrC,6LAAC;wCACC,SAAS,IAAM,eAAe,CAAA,OAAQ,CAAC;oDAAE,GAAG,IAAI;oDAAE,OAAO,CAAC,KAAK,KAAK;gDAAC,CAAC;wCACtE,WAAW,AAAC,gEAIX,OAHC,YAAY,KAAK,GACb,mDACA;;0DAGN,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAM,YAAY,KAAK,GAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;;;kCAM1C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAW,AAAC,sBAAiD,OAA5B,eAAe;;4CAClD;4CAAY;;;;;;;kDAEf,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;0CAG9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;4CACZ;4CAAW;;;;;;;kDAEd,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;0CAG9C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,AAAC,YAAY,SAAS,GAAG,MAAO,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAEpD,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;kCAKhD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;sCAA2B,kBAAkB;;;;;;;;;;;;;;;;;0BAK9D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAEzD,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAA+C;4CAC9C,YAAY,SAAS;4CAAC;;;;;;;kDAExC,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,YAAY,SAAS;wCAC5B,UAAU,CAAC,IAAM,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAC5D,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAA+C;4CAC/C,YAAY,QAAQ;4CAAC;;;;;;;kDAEtC,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,YAAY,QAAQ;wCAC3B,UAAU,CAAC,IAAM,qBAAqB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAC3D,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;;4CAA+C;4CAChD,YAAY,WAAW;4CAAC;;;;;;;kDAExC,6LAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,OAAO,YAAY,WAAW;wCAC9B,UAAU,CAAC,IAAM,wBAAwB,OAAO,EAAE,MAAM,CAAC,KAAK;wCAC9D,UAAU;wCACV,WAAU;;;;;;kDAEZ,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;0DACN,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAKV,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,QAAQ,CAAC,KAAK;gDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DACvC,GAAG,IAAI;4DACP,UAAU;gEAAE,GAAG,KAAK,QAAQ;gEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACtD,CAAC;gDACD,UAAU;gDACV,WAAU;;;;;;;;;;;;kDAGd,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,YAAY,QAAQ,CAAC,GAAG;gDAC/B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;4DACvC,GAAG,IAAI;4DACP,UAAU;gEAAE,GAAG,KAAK,QAAQ;gEAAE,KAAK,EAAE,MAAM,CAAC,KAAK;4DAAC;wDACpD,CAAC;gDACD,UAAU;gDACV,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAvQwB;KAAA", "debugId": null}}, {"offset": {"line": 1231, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/ProductionTracker.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Leaf, Calendar, TrendingUp, Package, Plus, Eye } from 'lucide-react';\n\ninterface PlantBatch {\n  id: string;\n  variety: string;\n  plantedDate: string;\n  expectedHarvest: string;\n  currentStage: string;\n  plantCount: number;\n  layer: number;\n  progress: number;\n  status: 'healthy' | 'warning' | 'critical';\n}\n\nexport default function ProductionTracker() {\n  const [batches, setBatches] = useState<PlantBatch[]>([\n    {\n      id: 'B001',\n      variety: 'Marul Rex',\n      plantedDate: '2024-01-15',\n      expectedHarvest: '2024-02-08',\n      currentStage: 'Büyüme',\n      plantCount: 320,\n      layer: 1,\n      progress: 75,\n      status: 'healthy'\n    },\n    {\n      id: 'B002',\n      variety: 'Butterhead',\n      plantedDate: '2024-01-20',\n      expectedHarvest: '2024-02-13',\n      currentStage: 'Gelişim',\n      plantCount: 280,\n      layer: 2,\n      progress: 60,\n      status: 'healthy'\n    },\n    {\n      id: 'B003',\n      variety: 'Marul Rex',\n      plantedDate: '2024-01-25',\n      expectedHarvest: '2024-02-18',\n      currentStage: 'Fide',\n      plantCount: 300,\n      layer: 3,\n      progress: 35,\n      status: 'warning'\n    },\n    {\n      id: 'B004',\n      variety: 'Lollo Rosso',\n      plantedDate: '2024-01-30',\n      expectedHarvest: '2024-02-23',\n      currentStage: 'Çimlenme',\n      plantCount: 250,\n      layer: 4,\n      progress: 15,\n      status: 'healthy'\n    }\n  ]);\n\n  const [showAddForm, setShowAddForm] = useState(false);\n  const [newBatch, setNewBatch] = useState({\n    variety: '',\n    plantCount: '',\n    layer: ''\n  });\n\n  const varieties = ['Marul Rex', 'Butterhead', 'Lollo Rosso', 'Iceberg', 'Romaine'];\n  const stages = ['Çimlenme', 'Fide', 'Gelişim', 'Büyüme', 'Hasat Hazır'];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'healthy': return 'bg-green-100 text-green-800';\n      case 'warning': return 'bg-yellow-100 text-yellow-800';\n      case 'critical': return 'bg-red-100 text-red-800';\n      default: return 'bg-gray-100 text-gray-800';\n    }\n  };\n\n  const getProgressColor = (progress: number) => {\n    if (progress >= 80) return 'bg-green-500';\n    if (progress >= 60) return 'bg-blue-500';\n    if (progress >= 40) return 'bg-yellow-500';\n    return 'bg-gray-500';\n  };\n\n  const calculateDaysRemaining = (harvestDate: string) => {\n    const today = new Date();\n    const harvest = new Date(harvestDate);\n    const diffTime = harvest.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n\n  const handleAddBatch = () => {\n    if (newBatch.variety && newBatch.plantCount && newBatch.layer) {\n      const today = new Date();\n      const harvestDate = new Date(today);\n      harvestDate.setDate(today.getDate() + 24); // 24 günlük döngü\n\n      const batch: PlantBatch = {\n        id: `B${String(batches.length + 1).padStart(3, '0')}`,\n        variety: newBatch.variety,\n        plantedDate: today.toISOString().split('T')[0],\n        expectedHarvest: harvestDate.toISOString().split('T')[0],\n        currentStage: 'Çimlenme',\n        plantCount: parseInt(newBatch.plantCount),\n        layer: parseInt(newBatch.layer),\n        progress: 5,\n        status: 'healthy'\n      };\n\n      setBatches([...batches, batch]);\n      setNewBatch({ variety: '', plantCount: '', layer: '' });\n      setShowAddForm(false);\n    }\n  };\n\n  const totalPlants = batches.reduce((sum, batch) => sum + batch.plantCount, 0);\n  const readyToHarvest = batches.filter(batch => batch.progress >= 90).length;\n  const averageProgress = batches.reduce((sum, batch) => sum + batch.progress, 0) / batches.length;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Özet Kartları */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n              <Leaf className=\"w-5 h-5 text-green-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Toplam Bitki</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{totalPlants.toLocaleString()}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Package className=\"w-5 h-5 text-blue-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Aktif Parti</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{batches.length}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Calendar className=\"w-5 h-5 text-purple-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Hasat Hazır</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{readyToHarvest}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n              <TrendingUp className=\"w-5 h-5 text-orange-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Ortalama İlerleme</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{averageProgress.toFixed(0)}%</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Parti Listesi */}\n      <div className=\"bg-white rounded-lg shadow\">\n        <div className=\"px-6 py-4 border-b border-gray-200\">\n          <div className=\"flex items-center justify-between\">\n            <h2 className=\"text-lg font-semibold text-gray-900\">Üretim Partileri</h2>\n            <button\n              onClick={() => setShowAddForm(true)}\n              className=\"flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700\"\n            >\n              <Plus className=\"w-4 h-4\" />\n              <span>Yeni Parti</span>\n            </button>\n          </div>\n        </div>\n\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Parti ID\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Çeşit\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Ekim Tarihi\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Hasat Tarihi\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Aşama\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Bitki Sayısı\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Kat\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  İlerleme\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Durum\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  İşlemler\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {batches.map((batch) => (\n                <tr key={batch.id} className=\"hover:bg-gray-50\">\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900\">\n                    {batch.id}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {batch.variety}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(batch.plantedDate).toLocaleDateString('tr-TR')}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {new Date(batch.expectedHarvest).toLocaleDateString('tr-TR')}\n                    <div className=\"text-xs text-gray-500\">\n                      {calculateDaysRemaining(batch.expectedHarvest)} gün kaldı\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {batch.currentStage}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    {batch.plantCount.toLocaleString()}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                    Kat {batch.layer}\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <div className=\"flex items-center space-x-2\">\n                      <div className=\"w-16 bg-gray-200 rounded-full h-2\">\n                        <div\n                          className={`h-2 rounded-full ${getProgressColor(batch.progress)}`}\n                          style={{ width: `${batch.progress}%` }}\n                        ></div>\n                      </div>\n                      <span className=\"text-sm text-gray-600\">{batch.progress}%</span>\n                    </div>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap\">\n                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(batch.status)}`}>\n                      {batch.status === 'healthy' ? 'Sağlıklı' : \n                       batch.status === 'warning' ? 'Dikkat' : 'Kritik'}\n                    </span>\n                  </td>\n                  <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                    <button className=\"text-blue-600 hover:text-blue-900\">\n                      <Eye className=\"w-4 h-4\" />\n                    </button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Yeni Parti Ekleme Formu */}\n      {showAddForm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 w-full max-w-md\">\n            <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Yeni Parti Ekle</h3>\n            \n            <div className=\"space-y-4\">\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Marul Çeşidi\n                </label>\n                <select\n                  value={newBatch.variety}\n                  onChange={(e) => setNewBatch(prev => ({ ...prev, variety: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                >\n                  <option value=\"\">Seçiniz</option>\n                  {varieties.map(variety => (\n                    <option key={variety} value={variety}>{variety}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Bitki Sayısı\n                </label>\n                <input\n                  type=\"number\"\n                  value={newBatch.plantCount}\n                  onChange={(e) => setNewBatch(prev => ({ ...prev, plantCount: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                  placeholder=\"Örn: 300\"\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                  Kat\n                </label>\n                <select\n                  value={newBatch.layer}\n                  onChange={(e) => setNewBatch(prev => ({ ...prev, layer: e.target.value }))}\n                  className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n                >\n                  <option value=\"\">Seçiniz</option>\n                  <option value=\"1\">Kat 1</option>\n                  <option value=\"2\">Kat 2</option>\n                  <option value=\"3\">Kat 3</option>\n                  <option value=\"4\">Kat 4</option>\n                </select>\n              </div>\n            </div>\n\n            <div className=\"flex space-x-3 mt-6\">\n              <button\n                onClick={handleAddBatch}\n                className=\"flex-1 bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700\"\n              >\n                Parti Ekle\n              </button>\n              <button\n                onClick={() => setShowAddForm(false)}\n                className=\"flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400\"\n              >\n                İptal\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAiBe,SAAS;;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QACnD;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QACA;YACE,IAAI;YACJ,SAAS;YACT,aAAa;YACb,iBAAiB;YACjB,cAAc;YACd,YAAY;YACZ,OAAO;YACP,UAAU;YACV,QAAQ;QACV;KACD;IAED,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,YAAY;QACZ,OAAO;IACT;IAEA,MAAM,YAAY;QAAC;QAAa;QAAc;QAAe;QAAW;KAAU;IAClF,MAAM,SAAS;QAAC;QAAY;QAAQ;QAAW;QAAU;KAAc;IAEvE,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,YAAY,IAAI,OAAO;QAC3B,IAAI,YAAY,IAAI,OAAO;QAC3B,IAAI,YAAY,IAAI,OAAO;QAC3B,OAAO;IACT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,QAAQ,IAAI;QAClB,MAAM,UAAU,IAAI,KAAK;QACzB,MAAM,WAAW,QAAQ,OAAO,KAAK,MAAM,OAAO;QAClD,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,SAAS,OAAO,IAAI,SAAS,UAAU,IAAI,SAAS,KAAK,EAAE;YAC7D,MAAM,QAAQ,IAAI;YAClB,MAAM,cAAc,IAAI,KAAK;YAC7B,YAAY,OAAO,CAAC,MAAM,OAAO,KAAK,KAAK,kBAAkB;YAE7D,MAAM,QAAoB;gBACxB,IAAI,AAAC,IAA+C,OAA5C,OAAO,QAAQ,MAAM,GAAG,GAAG,QAAQ,CAAC,GAAG;gBAC/C,SAAS,SAAS,OAAO;gBACzB,aAAa,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC9C,iBAAiB,YAAY,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACxD,cAAc;gBACd,YAAY,SAAS,SAAS,UAAU;gBACxC,OAAO,SAAS,SAAS,KAAK;gBAC9B,UAAU;gBACV,QAAQ;YACV;YAEA,WAAW;mBAAI;gBAAS;aAAM;YAC9B,YAAY;gBAAE,SAAS;gBAAI,YAAY;gBAAI,OAAO;YAAG;YACrD,eAAe;QACjB;IACF;IAEA,MAAM,cAAc,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,UAAU,EAAE;IAC3E,MAAM,iBAAiB,QAAQ,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,IAAI,IAAI,MAAM;IAC3E,MAAM,kBAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,QAAQ,EAAE,KAAK,QAAQ,MAAM;IAEhG,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;8CAElB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDAAoC,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;kCAKjF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,2MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;;;;;;8CAErB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDAAoC,QAAQ,MAAM;;;;;;;;;;;;;;;;;;;;;;;kCAKrE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;;;;;;8CAEtB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;kCAKvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAoC,gBAAgB,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOpF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;;sDAEV,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;;;;;;;kCAKZ,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,QAAQ,GAAG,CAAC,CAAC,sBACZ,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAG,WAAU;8DACX,MAAM,EAAE;;;;;;8DAEX,6LAAC;oDAAG,WAAU;8DACX,MAAM,OAAO;;;;;;8DAEhB,6LAAC;oDAAG,WAAU;8DACX,IAAI,KAAK,MAAM,WAAW,EAAE,kBAAkB,CAAC;;;;;;8DAElD,6LAAC;oDAAG,WAAU;;wDACX,IAAI,KAAK,MAAM,eAAe,EAAE,kBAAkB,CAAC;sEACpD,6LAAC;4DAAI,WAAU;;gEACZ,uBAAuB,MAAM,eAAe;gEAAE;;;;;;;;;;;;;8DAGnD,6LAAC;oDAAG,WAAU;8DACX,MAAM,YAAY;;;;;;8DAErB,6LAAC;oDAAG,WAAU;8DACX,MAAM,UAAU,CAAC,cAAc;;;;;;8DAElC,6LAAC;oDAAG,WAAU;;wDAAoD;wDAC3D,MAAM,KAAK;;;;;;;8DAElB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAW,AAAC,oBAAoD,OAAjC,iBAAiB,MAAM,QAAQ;oEAC9D,OAAO;wEAAE,OAAO,AAAC,GAAiB,OAAf,MAAM,QAAQ,EAAC;oEAAG;;;;;;;;;;;0EAGzC,6LAAC;gEAAK,WAAU;;oEAAyB,MAAM,QAAQ;oEAAC;;;;;;;;;;;;;;;;;;8DAG5D,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,AAAC,4DAAwF,OAA7B,eAAe,MAAM,MAAM;kEACrG,MAAM,MAAM,KAAK,YAAY,aAC7B,MAAM,MAAM,KAAK,YAAY,WAAW;;;;;;;;;;;8DAG7C,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAO,WAAU;kEAChB,cAAA,6LAAC,mMAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;;;;;;;;;;;;2CA5CZ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;YAuD1B,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAEzD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,OAAO;4CACvB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC1E,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;gDAChB,UAAU,GAAG,CAAC,CAAA,wBACb,6LAAC;wDAAqB,OAAO;kEAAU;uDAA1B;;;;;;;;;;;;;;;;;8CAKnB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,MAAK;4CACL,OAAO,SAAS,UAAU;4CAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,YAAY,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CAC7E,WAAU;4CACV,aAAY;;;;;;;;;;;;8CAIhB,6LAAC;;sDACC,6LAAC;4CAAM,WAAU;sDAA+C;;;;;;sDAGhE,6LAAC;4CACC,OAAO,SAAS,KAAK;4CACrB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oDAAC,CAAC;4CACxE,WAAU;;8DAEV,6LAAC;oDAAO,OAAM;8DAAG;;;;;;8DACjB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;8DAClB,6LAAC;oDAAO,OAAM;8DAAI;;;;;;;;;;;;;;;;;;;;;;;;sCAKxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAvVwB;KAAA", "debugId": null}}, {"offset": {"line": 2164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/EnergyCalculator.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Zap, DollarSign, TrendingDown, Calculator, Lightbulb, Settings } from 'lucide-react';\n\ninterface EnergySettings {\n  systemArea: number; // m²\n  layers: number;\n  ledModules: number;\n  powerPerModule: number; // W\n  operatingHours: number;\n  electricityRate: number; // TL/kWh\n  ppfdLevel: number;\n  efficiency: number; // %\n}\n\ninterface EnergyData {\n  dailyConsumption: number; // kWh\n  monthlyConsumption: number; // kWh\n  dailyCost: number; // TL\n  monthlyCost: number; // TL\n  costPerPlant: number; // TL\n  co2Emissions: number; // kg CO2\n}\n\nexport default function EnergyCalculator() {\n  const [settings, setSettings] = useState<EnergySettings>({\n    systemArea: 100, // 4 kat x 25 m²\n    layers: 4,\n    ledModules: 167,\n    powerPerModule: 50, // Samsung LM301H EVO\n    operatingHours: 14,\n    electricityRate: 2.67, // TL/kWh (Türkiye ortalama)\n    ppfdLevel: 275,\n    efficiency: 75\n  });\n\n  const [energyData, setEnergyData] = useState<EnergyData>({\n    dailyConsumption: 0,\n    monthlyConsumption: 0,\n    dailyCost: 0,\n    monthlyCost: 0,\n    costPerPlant: 0,\n    co2Emissions: 0\n  });\n\n  const [optimizationMode, setOptimizationMode] = useState<'performance' | 'efficiency' | 'balanced'>('balanced');\n\n  // Enerji hesaplamaları\n  useEffect(() => {\n    const totalPower = (settings.ledModules * settings.powerPerModule * settings.efficiency) / 100; // kW\n    const dailyConsumption = (totalPower * settings.operatingHours) / 1000; // kWh\n    const monthlyConsumption = dailyConsumption * 30;\n    const dailyCost = dailyConsumption * settings.electricityRate;\n    const monthlyCost = monthlyConsumption * settings.electricityRate;\n    \n    // Bitki başına maliyet (1600 bitki/ay varsayımı)\n    const plantsPerMonth = settings.systemArea * 16; // m² başına 16 bitki\n    const costPerPlant = monthlyCost / plantsPerMonth;\n    \n    // CO2 emisyonu (Türkiye elektrik karışımı: ~0.5 kg CO2/kWh)\n    const co2Emissions = monthlyConsumption * 0.5;\n\n    setEnergyData({\n      dailyConsumption,\n      monthlyConsumption,\n      dailyCost,\n      monthlyCost,\n      costPerPlant,\n      co2Emissions\n    });\n  }, [settings]);\n\n  const getOptimizationSettings = (mode: string) => {\n    switch (mode) {\n      case 'performance':\n        return {\n          ppfdLevel: 300,\n          operatingHours: 16,\n          efficiency: 85,\n          description: 'Maksimum verim için yüksek ışık ve uzun fotoperiyot'\n        };\n      case 'efficiency':\n        return {\n          ppfdLevel: 220,\n          operatingHours: 12,\n          efficiency: 65,\n          description: 'Enerji tasarrufu için düşük ışık ve kısa fotoperiyot'\n        };\n      case 'balanced':\n        return {\n          ppfdLevel: 275,\n          operatingHours: 14,\n          efficiency: 75,\n          description: 'Verim ve enerji dengesinde optimal ayarlar'\n        };\n      default:\n        return settings;\n    }\n  };\n\n  const applyOptimization = (mode: string) => {\n    const optimized = getOptimizationSettings(mode);\n    setSettings(prev => ({\n      ...prev,\n      ppfdLevel: optimized.ppfdLevel,\n      operatingHours: optimized.operatingHours,\n      efficiency: optimized.efficiency\n    }));\n    setOptimizationMode(mode as any);\n  };\n\n  const calculateSavings = () => {\n    const currentCost = energyData.monthlyCost;\n    const efficientSettings = getOptimizationSettings('efficiency');\n    const efficientPower = (settings.ledModules * settings.powerPerModule * efficientSettings.efficiency) / 100;\n    const efficientConsumption = (efficientPower * efficientSettings.operatingHours) / 1000 * 30;\n    const efficientCost = efficientConsumption * settings.electricityRate;\n    \n    return {\n      savings: currentCost - efficientCost,\n      percentage: ((currentCost - efficientCost) / currentCost) * 100\n    };\n  };\n\n  const savings = calculateSavings();\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Enerji Özeti */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n              <Zap className=\"w-5 h-5 text-blue-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Günlük Tüketim</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{energyData.dailyConsumption.toFixed(1)} kWh</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n              <DollarSign className=\"w-5 h-5 text-green-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Aylık Maliyet</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₺{energyData.monthlyCost.toFixed(0)}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n              <Calculator className=\"w-5 h-5 text-purple-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">Bitki Başı Maliyet</p>\n              <p className=\"text-2xl font-bold text-gray-900\">₺{energyData.costPerPlant.toFixed(2)}</p>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"bg-white rounded-lg shadow p-6\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center\">\n              <TrendingDown className=\"w-5 h-5 text-red-600\" />\n            </div>\n            <div>\n              <p className=\"text-sm text-gray-500\">CO₂ Emisyonu</p>\n              <p className=\"text-2xl font-bold text-gray-900\">{energyData.co2Emissions.toFixed(0)} kg</p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Optimizasyon Modları */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Optimizasyon Modları</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          {['performance', 'balanced', 'efficiency'].map((mode) => {\n            const config = getOptimizationSettings(mode);\n            const isActive = optimizationMode === mode;\n            \n            return (\n              <div\n                key={mode}\n                className={`border-2 rounded-lg p-4 cursor-pointer transition-colors ${\n                  isActive ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'\n                }`}\n                onClick={() => applyOptimization(mode)}\n              >\n                <div className=\"flex items-center space-x-2 mb-2\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    mode === 'performance' ? 'bg-red-500' :\n                    mode === 'balanced' ? 'bg-yellow-500' : 'bg-green-500'\n                  }`}></div>\n                  <h3 className=\"font-medium text-gray-900\">\n                    {mode === 'performance' ? 'Yüksek Performans' :\n                     mode === 'balanced' ? 'Dengeli' : 'Enerji Tasarrufu'}\n                  </h3>\n                </div>\n                <p className=\"text-sm text-gray-600 mb-3\">{config.description}</p>\n                <div className=\"space-y-1 text-xs text-gray-500\">\n                  <div>PPFD: {config.ppfdLevel} µmol/m²/s</div>\n                  <div>Fotoperiyot: {config.operatingHours} saat</div>\n                  <div>Verimlilik: {config.efficiency}%</div>\n                </div>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Detaylı Ayarlar */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Sistem Ayarları</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Sistem Alanı (m²)\n              </label>\n              <input\n                type=\"number\"\n                value={settings.systemArea}\n                onChange={(e) => setSettings(prev => ({ ...prev, systemArea: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Kat Sayısı\n              </label>\n              <input\n                type=\"number\"\n                value={settings.layers}\n                onChange={(e) => setSettings(prev => ({ ...prev, layers: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                LED Modül Sayısı\n              </label>\n              <input\n                type=\"number\"\n                value={settings.ledModules}\n                onChange={(e) => setSettings(prev => ({ ...prev, ledModules: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Modül Başı Güç (W)\n              </label>\n              <input\n                type=\"number\"\n                value={settings.powerPerModule}\n                onChange={(e) => setSettings(prev => ({ ...prev, powerPerModule: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n          </div>\n\n          <div className=\"space-y-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Günlük Çalışma Saati\n              </label>\n              <input\n                type=\"number\"\n                value={settings.operatingHours}\n                onChange={(e) => setSettings(prev => ({ ...prev, operatingHours: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Elektrik Fiyatı (₺/kWh)\n              </label>\n              <input\n                type=\"number\"\n                step=\"0.01\"\n                value={settings.electricityRate}\n                onChange={(e) => setSettings(prev => ({ ...prev, electricityRate: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                PPFD Seviyesi (µmol/m²/s)\n              </label>\n              <input\n                type=\"number\"\n                value={settings.ppfdLevel}\n                onChange={(e) => setSettings(prev => ({ ...prev, ppfdLevel: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Sistem Verimliliği (%)\n              </label>\n              <input\n                type=\"number\"\n                value={settings.efficiency}\n                onChange={(e) => setSettings(prev => ({ ...prev, efficiency: Number(e.target.value) }))}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n              />\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tasarruf Analizi */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Tasarruf Potansiyeli</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"bg-green-50 rounded-lg p-4\">\n            <h3 className=\"font-medium text-green-800 mb-2\">Enerji Tasarrufu Modu</h3>\n            <div className=\"space-y-2\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-green-700\">Aylık Tasarruf:</span>\n                <span className=\"font-medium text-green-800\">₺{savings.savings.toFixed(0)}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-green-700\">Tasarruf Oranı:</span>\n                <span className=\"font-medium text-green-800\">{savings.percentage.toFixed(1)}%</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-green-700\">Yıllık Tasarruf:</span>\n                <span className=\"font-medium text-green-800\">₺{(savings.savings * 12).toFixed(0)}</span>\n              </div>\n            </div>\n          </div>\n\n          <div className=\"bg-blue-50 rounded-lg p-4\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">Kozai Önerileri</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• PPFD'yi 200-220 µmol/m²/s'e düşür</li>\n              <li>• Fotoperiyodu 12 saate indir</li>\n              <li>• CO₂ yoksa ışığı kıs</li>\n              <li>• Hasat döngüsünü 25-30 güne çıkar</li>\n              <li>• LED mesafesini 35-40 cm'e ayarla</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;;;AAHA;;;AAyBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,YAAY;QACZ,QAAQ;QACR,YAAY;QACZ,gBAAgB;QAChB,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,YAAY;IACd;IAEA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,kBAAkB;QAClB,oBAAoB;QACpB,WAAW;QACX,aAAa;QACb,cAAc;QACd,cAAc;IAChB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA6C;IAEpG,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,aAAa,AAAC,SAAS,UAAU,GAAG,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI,KAAK,KAAK;YACrG,MAAM,mBAAmB,AAAC,aAAa,SAAS,cAAc,GAAI,MAAM,MAAM;YAC9E,MAAM,qBAAqB,mBAAmB;YAC9C,MAAM,YAAY,mBAAmB,SAAS,eAAe;YAC7D,MAAM,cAAc,qBAAqB,SAAS,eAAe;YAEjE,iDAAiD;YACjD,MAAM,iBAAiB,SAAS,UAAU,GAAG,IAAI,qBAAqB;YACtE,MAAM,eAAe,cAAc;YAEnC,4DAA4D;YAC5D,MAAM,eAAe,qBAAqB;YAE1C,cAAc;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;YACF;QACF;qCAAG;QAAC;KAAS;IAEb,MAAM,0BAA0B,CAAC;QAC/B,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,gBAAgB;oBAChB,YAAY;oBACZ,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,gBAAgB;oBAChB,YAAY;oBACZ,aAAa;gBACf;YACF,KAAK;gBACH,OAAO;oBACL,WAAW;oBACX,gBAAgB;oBAChB,YAAY;oBACZ,aAAa;gBACf;YACF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,YAAY,wBAAwB;QAC1C,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,WAAW,UAAU,SAAS;gBAC9B,gBAAgB,UAAU,cAAc;gBACxC,YAAY,UAAU,UAAU;YAClC,CAAC;QACD,oBAAoB;IACtB;IAEA,MAAM,mBAAmB;QACvB,MAAM,cAAc,WAAW,WAAW;QAC1C,MAAM,oBAAoB,wBAAwB;QAClD,MAAM,iBAAiB,AAAC,SAAS,UAAU,GAAG,SAAS,cAAc,GAAG,kBAAkB,UAAU,GAAI;QACxG,MAAM,uBAAuB,AAAC,iBAAiB,kBAAkB,cAAc,GAAI,OAAO;QAC1F,MAAM,gBAAgB,uBAAuB,SAAS,eAAe;QAErE,OAAO;YACL,SAAS,cAAc;YACvB,YAAY,AAAC,CAAC,cAAc,aAAa,IAAI,cAAe;QAC9D;IACF;IAEA,MAAM,UAAU;IAEhB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAoC,WAAW,gBAAgB,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;kCAK9F,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,WAAW,WAAW,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAKvF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;8CAExB,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAmC;gDAAE,WAAW,YAAY,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;kCAKxF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,yNAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;8CAE1B,6LAAC;;sDACC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDACrC,6LAAC;4CAAE,WAAU;;gDAAoC,WAAW,YAAY,CAAC,OAAO,CAAC;gDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAe;4BAAY;yBAAa,CAAC,GAAG,CAAC,CAAC;4BAC9C,MAAM,SAAS,wBAAwB;4BACvC,MAAM,WAAW,qBAAqB;4BAEtC,qBACE,6LAAC;gCAEC,WAAW,AAAC,4DAEX,OADC,WAAW,iCAAiC;gCAE9C,SAAS,IAAM,kBAAkB;;kDAEjC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,wBAGhB,OAFC,SAAS,gBAAgB,eACzB,SAAS,aAAa,kBAAkB;;;;;;0DAE1C,6LAAC;gDAAG,WAAU;0DACX,SAAS,gBAAgB,sBACzB,SAAS,aAAa,YAAY;;;;;;;;;;;;kDAGvC,6LAAC;wCAAE,WAAU;kDAA8B,OAAO,WAAW;;;;;;kDAC7D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;oDAAI;oDAAO,OAAO,SAAS;oDAAC;;;;;;;0DAC7B,6LAAC;;oDAAI;oDAAc,OAAO,cAAc;oDAAC;;;;;;;0DACzC,6LAAC;;oDAAI;oDAAa,OAAO,UAAU;oDAAC;;;;;;;;;;;;;;+BApBjC;;;;;wBAwBX;;;;;;;;;;;;0BAKJ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACrF,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,MAAM;gDACtB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,QAAQ,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACjF,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACrF,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACzF,WAAU;;;;;;;;;;;;;;;;;;0CAKhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,cAAc;gDAC9B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,gBAAgB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACzF,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,MAAK;gDACL,OAAO,SAAS,eAAe;gDAC/B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,iBAAiB,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDAC1F,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,SAAS;gDACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACpF,WAAU;;;;;;;;;;;;kDAId,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6LAAC;gDACC,MAAK;gDACL,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wDAAE,CAAC;gDACrF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAkC;;;;;;kDAChD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,6LAAC;wDAAK,WAAU;;4DAA6B;4DAAE,QAAQ,OAAO,CAAC,OAAO,CAAC;;;;;;;;;;;;;0DAEzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,6LAAC;wDAAK,WAAU;;4DAA8B,QAAQ,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;0DAE9E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAyB;;;;;;kEACzC,6LAAC;wDAAK,WAAU;;4DAA6B;4DAAE,CAAC,QAAQ,OAAO,GAAG,EAAE,EAAE,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0CAKpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAC/C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAhVwB;KAAA", "debugId": null}}, {"offset": {"line": 3123, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/SensorData.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Thermometer, Droplets, Wind, Zap, Activity, AlertCircle } from 'lucide-react';\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts';\n\ninterface SensorReading {\n  timestamp: string;\n  temperature: number;\n  humidity: number;\n  ph: number;\n  ec: number;\n  co2: number;\n  airflow: number;\n}\n\ninterface SensorStatus {\n  id: string;\n  name: string;\n  type: string;\n  status: 'online' | 'offline' | 'warning';\n  lastReading: string;\n  location: string;\n}\n\nexport default function SensorData() {\n  const [currentReadings, setCurrentReadings] = useState<SensorReading>({\n    timestamp: new Date().toISOString(),\n    temperature: 22.5,\n    humidity: 65,\n    ph: 6.1,\n    ec: 1.6,\n    co2: 850,\n    airflow: 2.3\n  });\n\n  const [historicalData, setHistoricalData] = useState<SensorReading[]>([]);\n  const [sensors, setSensors] = useState<SensorStatus[]>([\n    { id: 'T001', name: 'Sıcaklık Sensörü 1', type: 'temperature', status: 'online', lastReading: '2 saniye önce', location: 'Kat 1' },\n    { id: 'T002', name: 'Sıcaklık Sensörü 2', type: 'temperature', status: 'online', lastReading: '3 saniye önce', location: 'Kat 2' },\n    { id: 'H001', name: 'Nem Sensörü 1', type: 'humidity', status: 'online', lastReading: '1 saniye önce', location: 'Kat 1' },\n    { id: 'H002', name: 'Nem Sensörü 2', type: 'humidity', status: 'warning', lastReading: '45 saniye önce', location: 'Kat 3' },\n    { id: 'PH01', name: 'pH Sensörü', type: 'ph', status: 'online', lastReading: '5 saniye önce', location: 'Besin Tankı' },\n    { id: 'EC01', name: 'EC Sensörü', type: 'ec', status: 'online', lastReading: '4 saniye önce', location: 'Besin Tankı' },\n    { id: 'CO01', name: 'CO₂ Sensörü', type: 'co2', status: 'offline', lastReading: '2 dakika önce', location: 'Ana Oda' },\n    { id: 'AF01', name: 'Hava Akış Sensörü', type: 'airflow', status: 'online', lastReading: '1 saniye önce', location: 'Ventilasyon' }\n  ]);\n\n  const [selectedTimeRange, setSelectedTimeRange] = useState('24h');\n\n  // Simüle edilmiş gerçek zamanlı veri\n  useEffect(() => {\n    const generateHistoricalData = () => {\n      const data = [];\n      const now = new Date();\n      \n      for (let i = 23; i >= 0; i--) {\n        const timestamp = new Date(now.getTime() - i * 60 * 60 * 1000);\n        data.push({\n          timestamp: timestamp.toISOString(),\n          temperature: 22 + Math.random() * 3,\n          humidity: 60 + Math.random() * 15,\n          ph: 5.8 + Math.random() * 0.8,\n          ec: 1.4 + Math.random() * 0.6,\n          co2: 800 + Math.random() * 300,\n          airflow: 2 + Math.random() * 1\n        });\n      }\n      \n      setHistoricalData(data);\n    };\n\n    generateHistoricalData();\n\n    const interval = setInterval(() => {\n      setCurrentReadings(prev => ({\n        timestamp: new Date().toISOString(),\n        temperature: prev.temperature + (Math.random() - 0.5) * 0.3,\n        humidity: prev.humidity + (Math.random() - 0.5) * 2,\n        ph: Math.max(5.5, Math.min(6.5, prev.ph + (Math.random() - 0.5) * 0.1)),\n        ec: Math.max(1.2, Math.min(2.0, prev.ec + (Math.random() - 0.5) * 0.05)),\n        co2: prev.co2 + (Math.random() - 0.5) * 20,\n        airflow: Math.max(1.5, Math.min(3.5, prev.airflow + (Math.random() - 0.5) * 0.2))\n      }));\n\n      // Geçmiş verileri güncelle\n      setHistoricalData(prev => {\n        const newData = [...prev.slice(1), {\n          timestamp: new Date().toISOString(),\n          temperature: currentReadings.temperature,\n          humidity: currentReadings.humidity,\n          ph: currentReadings.ph,\n          ec: currentReadings.ec,\n          co2: currentReadings.co2,\n          airflow: currentReadings.airflow\n        }];\n        return newData;\n      });\n    }, 3000);\n\n    return () => clearInterval(interval);\n  }, [currentReadings]);\n\n  const getSensorIcon = (type: string) => {\n    switch (type) {\n      case 'temperature': return Thermometer;\n      case 'humidity': return Droplets;\n      case 'ph': return Droplets;\n      case 'ec': return Zap;\n      case 'co2': return Wind;\n      case 'airflow': return Activity;\n      default: return Activity;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'online': return 'text-green-600 bg-green-100';\n      case 'warning': return 'text-yellow-600 bg-yellow-100';\n      case 'offline': return 'text-red-600 bg-red-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getValueStatus = (type: string, value: number) => {\n    const ranges = {\n      temperature: { min: 20, max: 24 },\n      humidity: { min: 60, max: 70 },\n      ph: { min: 5.8, max: 6.3 },\n      ec: { min: 1.4, max: 1.8 },\n      co2: { min: 700, max: 1000 },\n      airflow: { min: 2.0, max: 3.0 }\n    };\n\n    const range = ranges[type as keyof typeof ranges];\n    if (!range) return 'normal';\n\n    if (value < range.min * 0.9 || value > range.max * 1.1) return 'critical';\n    if (value < range.min || value > range.max) return 'warning';\n    return 'normal';\n  };\n\n  const formatChartData = (data: SensorReading[]) => {\n    return data.map(reading => ({\n      ...reading,\n      time: new Date(reading.timestamp).toLocaleTimeString('tr-TR', { \n        hour: '2-digit', \n        minute: '2-digit' \n      })\n    }));\n  };\n\n  const chartData = formatChartData(historicalData);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Anlık Değerler */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n        {[\n          { key: 'temperature', name: 'Sıcaklık', value: currentReadings.temperature, unit: '°C', icon: Thermometer },\n          { key: 'humidity', name: 'Nem', value: currentReadings.humidity, unit: '%', icon: Droplets },\n          { key: 'ph', name: 'pH', value: currentReadings.ph, unit: '', icon: Droplets },\n          { key: 'ec', name: 'EC', value: currentReadings.ec, unit: 'mS/cm', icon: Zap },\n          { key: 'co2', name: 'CO₂', value: currentReadings.co2, unit: 'ppm', icon: Wind },\n          { key: 'airflow', name: 'Hava Akışı', value: currentReadings.airflow, unit: 'm/s', icon: Activity }\n        ].map((sensor) => {\n          const Icon = sensor.icon;\n          const status = getValueStatus(sensor.key, sensor.value);\n          \n          return (\n            <div key={sensor.key} className=\"bg-white rounded-lg shadow p-4\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <Icon className={`w-5 h-5 ${\n                  status === 'critical' ? 'text-red-500' :\n                  status === 'warning' ? 'text-yellow-500' : 'text-green-500'\n                }`} />\n                <div className={`w-3 h-3 rounded-full ${\n                  status === 'critical' ? 'bg-red-500' :\n                  status === 'warning' ? 'bg-yellow-500' : 'bg-green-500'\n                }`}></div>\n              </div>\n              <div className=\"text-sm font-medium text-gray-700 mb-1\">{sensor.name}</div>\n              <div className=\"text-xl font-bold text-gray-900\">\n                {sensor.value.toFixed(sensor.key === 'co2' ? 0 : 1)}{sensor.unit}\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Grafik Bölümü */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Sensör Verileri Trendi</h2>\n          <select\n            value={selectedTimeRange}\n            onChange={(e) => setSelectedTimeRange(e.target.value)}\n            className=\"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500\"\n          >\n            <option value=\"1h\">Son 1 Saat</option>\n            <option value=\"24h\">Son 24 Saat</option>\n            <option value=\"7d\">Son 7 Gün</option>\n            <option value=\"30d\">Son 30 Gün</option>\n          </select>\n        </div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Sıcaklık ve Nem */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Sıcaklık & Nem</h3>\n            <ResponsiveContainer width=\"100%\" height={200}>\n              <LineChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Line type=\"monotone\" dataKey=\"temperature\" stroke=\"#ef4444\" name=\"Sıcaklık (°C)\" />\n                <Line type=\"monotone\" dataKey=\"humidity\" stroke=\"#3b82f6\" name=\"Nem (%)\" />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* pH ve EC */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">pH & EC</h3>\n            <ResponsiveContainer width=\"100%\" height={200}>\n              <LineChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Line type=\"monotone\" dataKey=\"ph\" stroke=\"#8b5cf6\" name=\"pH\" />\n                <Line type=\"monotone\" dataKey=\"ec\" stroke=\"#f59e0b\" name=\"EC (mS/cm)\" />\n              </LineChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* CO₂ */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">CO₂ Seviyesi</h3>\n            <ResponsiveContainer width=\"100%\" height={200}>\n              <AreaChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Area type=\"monotone\" dataKey=\"co2\" stroke=\"#10b981\" fill=\"#10b981\" fillOpacity={0.3} name=\"CO₂ (ppm)\" />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n\n          {/* Hava Akışı */}\n          <div>\n            <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Hava Akışı</h3>\n            <ResponsiveContainer width=\"100%\" height={200}>\n              <AreaChart data={chartData}>\n                <CartesianGrid strokeDasharray=\"3 3\" />\n                <XAxis dataKey=\"time\" />\n                <YAxis />\n                <Tooltip />\n                <Area type=\"monotone\" dataKey=\"airflow\" stroke=\"#6366f1\" fill=\"#6366f1\" fillOpacity={0.3} name=\"Hava Akışı (m/s)\" />\n              </AreaChart>\n            </ResponsiveContainer>\n          </div>\n        </div>\n      </div>\n\n      {/* Sensör Durumu */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Sensör Durumu</h2>\n        <div className=\"overflow-x-auto\">\n          <table className=\"w-full\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Sensör\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Tür\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Konum\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Durum\n                </th>\n                <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  Son Okuma\n                </th>\n              </tr>\n            </thead>\n            <tbody className=\"bg-white divide-y divide-gray-200\">\n              {sensors.map((sensor) => {\n                const Icon = getSensorIcon(sensor.type);\n                \n                return (\n                  <tr key={sensor.id} className=\"hover:bg-gray-50\">\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <div className=\"flex items-center space-x-3\">\n                        <Icon className=\"w-5 h-5 text-gray-400\" />\n                        <div>\n                          <div className=\"text-sm font-medium text-gray-900\">{sensor.name}</div>\n                          <div className=\"text-sm text-gray-500\">{sensor.id}</div>\n                        </div>\n                      </div>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {sensor.type === 'temperature' ? 'Sıcaklık' :\n                       sensor.type === 'humidity' ? 'Nem' :\n                       sensor.type === 'ph' ? 'pH' :\n                       sensor.type === 'ec' ? 'EC' :\n                       sensor.type === 'co2' ? 'CO₂' : 'Hava Akışı'}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                      {sensor.location}\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap\">\n                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(sensor.status)}`}>\n                        {sensor.status === 'online' ? 'Çevrimiçi' :\n                         sensor.status === 'warning' ? 'Uyarı' : 'Çevrimdışı'}\n                      </span>\n                    </td>\n                    <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                      {sensor.lastReading}\n                    </td>\n                  </tr>\n                );\n              })}\n            </tbody>\n          </table>\n        </div>\n      </div>\n\n      {/* Uyarılar */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h2 className=\"text-lg font-semibold text-gray-900 mb-4\">Sensör Uyarıları</h2>\n        <div className=\"space-y-3\">\n          <div className=\"flex items-start space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <AlertCircle className=\"w-5 h-5 text-yellow-500 mt-0.5\" />\n            <div>\n              <p className=\"text-sm font-medium text-yellow-800\">Nem Sensörü H002 Uyarısı</p>\n              <p className=\"text-xs text-yellow-700\">Kat 3'teki nem sensörü 45 saniyedir veri göndermiyor.</p>\n            </div>\n          </div>\n          \n          <div className=\"flex items-start space-x-3 p-3 bg-red-50 border border-red-200 rounded-lg\">\n            <AlertCircle className=\"w-5 h-5 text-red-500 mt-0.5\" />\n            <div>\n              <p className=\"text-sm font-medium text-red-800\">CO₂ Sensörü Çevrimdışı</p>\n              <p className=\"text-xs text-red-700\">Ana odadaki CO₂ sensörü 2 dakikadır çevrimdışı.</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAJA;;;;AAyBe,SAAS;;IACtB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;QACpE,WAAW,IAAI,OAAO,WAAW;QACjC,aAAa;QACb,UAAU;QACV,IAAI;QACJ,IAAI;QACJ,KAAK;QACL,SAAS;IACX;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB;QACrD;YAAE,IAAI;YAAQ,MAAM;YAAsB,MAAM;YAAe,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAQ;QACjI;YAAE,IAAI;YAAQ,MAAM;YAAsB,MAAM;YAAe,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAQ;QACjI;YAAE,IAAI;YAAQ,MAAM;YAAiB,MAAM;YAAY,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAQ;QACzH;YAAE,IAAI;YAAQ,MAAM;YAAiB,MAAM;YAAY,QAAQ;YAAW,aAAa;YAAkB,UAAU;QAAQ;QAC3H;YAAE,IAAI;YAAQ,MAAM;YAAc,MAAM;YAAM,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAc;QACtH;YAAE,IAAI;YAAQ,MAAM;YAAc,MAAM;YAAM,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAc;QACtH;YAAE,IAAI;YAAQ,MAAM;YAAe,MAAM;YAAO,QAAQ;YAAW,aAAa;YAAiB,UAAU;QAAU;QACrH;YAAE,IAAI;YAAQ,MAAM;YAAqB,MAAM;YAAW,QAAQ;YAAU,aAAa;YAAiB,UAAU;QAAc;KACnI;IAED,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM;+DAAyB;oBAC7B,MAAM,OAAO,EAAE;oBACf,MAAM,MAAM,IAAI;oBAEhB,IAAK,IAAI,IAAI,IAAI,KAAK,GAAG,IAAK;wBAC5B,MAAM,YAAY,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK;wBACzD,KAAK,IAAI,CAAC;4BACR,WAAW,UAAU,WAAW;4BAChC,aAAa,KAAK,KAAK,MAAM,KAAK;4BAClC,UAAU,KAAK,KAAK,MAAM,KAAK;4BAC/B,IAAI,MAAM,KAAK,MAAM,KAAK;4BAC1B,IAAI,MAAM,KAAK,MAAM,KAAK;4BAC1B,KAAK,MAAM,KAAK,MAAM,KAAK;4BAC3B,SAAS,IAAI,KAAK,MAAM,KAAK;wBAC/B;oBACF;oBAEA,kBAAkB;gBACpB;;YAEA;YAEA,MAAM,WAAW;iDAAY;oBAC3B;yDAAmB,CAAA,OAAQ,CAAC;gCAC1B,WAAW,IAAI,OAAO,WAAW;gCACjC,aAAa,KAAK,WAAW,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxD,UAAU,KAAK,QAAQ,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAClD,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAClE,IAAI,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCAClE,KAAK,KAAK,GAAG,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gCACxC,SAAS,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,KAAK,OAAO,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;4BAC9E,CAAC;;oBAED,2BAA2B;oBAC3B;yDAAkB,CAAA;4BAChB,MAAM,UAAU;mCAAI,KAAK,KAAK,CAAC;gCAAI;oCACjC,WAAW,IAAI,OAAO,WAAW;oCACjC,aAAa,gBAAgB,WAAW;oCACxC,UAAU,gBAAgB,QAAQ;oCAClC,IAAI,gBAAgB,EAAE;oCACtB,IAAI,gBAAgB,EAAE;oCACtB,KAAK,gBAAgB,GAAG;oCACxB,SAAS,gBAAgB,OAAO;gCAClC;6BAAE;4BACF,OAAO;wBACT;;gBACF;gDAAG;YAEH;wCAAO,IAAM,cAAc;;QAC7B;+BAAG;QAAC;KAAgB;IAEpB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAe,OAAO,mNAAA,CAAA,cAAW;YACtC,KAAK;gBAAY,OAAO,6MAAA,CAAA,WAAQ;YAChC,KAAK;gBAAM,OAAO,6MAAA,CAAA,WAAQ;YAC1B,KAAK;gBAAM,OAAO,mMAAA,CAAA,MAAG;YACrB,KAAK;gBAAO,OAAO,qMAAA,CAAA,OAAI;YACvB,KAAK;gBAAW,OAAO,6MAAA,CAAA,WAAQ;YAC/B;gBAAS,OAAO,6MAAA,CAAA,WAAQ;QAC1B;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC,MAAc;QACpC,MAAM,SAAS;YACb,aAAa;gBAAE,KAAK;gBAAI,KAAK;YAAG;YAChC,UAAU;gBAAE,KAAK;gBAAI,KAAK;YAAG;YAC7B,IAAI;gBAAE,KAAK;gBAAK,KAAK;YAAI;YACzB,IAAI;gBAAE,KAAK;gBAAK,KAAK;YAAI;YACzB,KAAK;gBAAE,KAAK;gBAAK,KAAK;YAAK;YAC3B,SAAS;gBAAE,KAAK;gBAAK,KAAK;YAAI;QAChC;QAEA,MAAM,QAAQ,MAAM,CAAC,KAA4B;QACjD,IAAI,CAAC,OAAO,OAAO;QAEnB,IAAI,QAAQ,MAAM,GAAG,GAAG,OAAO,QAAQ,MAAM,GAAG,GAAG,KAAK,OAAO;QAC/D,IAAI,QAAQ,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG,EAAE,OAAO;QACnD,OAAO;IACT;IAEA,MAAM,kBAAkB,CAAC;QACvB,OAAO,KAAK,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC1B,GAAG,OAAO;gBACV,MAAM,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC,SAAS;oBAC5D,MAAM;oBACN,QAAQ;gBACV;YACF,CAAC;IACH;IAEA,MAAM,YAAY,gBAAgB;IAElC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACZ;oBACC;wBAAE,KAAK;wBAAe,MAAM;wBAAY,OAAO,gBAAgB,WAAW;wBAAE,MAAM;wBAAM,MAAM,mNAAA,CAAA,cAAW;oBAAC;oBAC1G;wBAAE,KAAK;wBAAY,MAAM;wBAAO,OAAO,gBAAgB,QAAQ;wBAAE,MAAM;wBAAK,MAAM,6MAAA,CAAA,WAAQ;oBAAC;oBAC3F;wBAAE,KAAK;wBAAM,MAAM;wBAAM,OAAO,gBAAgB,EAAE;wBAAE,MAAM;wBAAI,MAAM,6MAAA,CAAA,WAAQ;oBAAC;oBAC7E;wBAAE,KAAK;wBAAM,MAAM;wBAAM,OAAO,gBAAgB,EAAE;wBAAE,MAAM;wBAAS,MAAM,mMAAA,CAAA,MAAG;oBAAC;oBAC7E;wBAAE,KAAK;wBAAO,MAAM;wBAAO,OAAO,gBAAgB,GAAG;wBAAE,MAAM;wBAAO,MAAM,qMAAA,CAAA,OAAI;oBAAC;oBAC/E;wBAAE,KAAK;wBAAW,MAAM;wBAAc,OAAO,gBAAgB,OAAO;wBAAE,MAAM;wBAAO,MAAM,6MAAA,CAAA,WAAQ;oBAAC;iBACnG,CAAC,GAAG,CAAC,CAAC;oBACL,MAAM,OAAO,OAAO,IAAI;oBACxB,MAAM,SAAS,eAAe,OAAO,GAAG,EAAE,OAAO,KAAK;oBAEtD,qBACE,6LAAC;wBAAqB,WAAU;;0CAC9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAW,AAAC,WAGjB,OAFC,WAAW,aAAa,iBACxB,WAAW,YAAY,oBAAoB;;;;;;kDAE7C,6LAAC;wCAAI,WAAW,AAAC,wBAGhB,OAFC,WAAW,aAAa,eACxB,WAAW,YAAY,kBAAkB;;;;;;;;;;;;0CAG7C,6LAAC;gCAAI,WAAU;0CAA0C,OAAO,IAAI;;;;;;0CACpE,6LAAC;gCAAI,WAAU;;oCACZ,OAAO,KAAK,CAAC,OAAO,CAAC,OAAO,GAAG,KAAK,QAAQ,IAAI;oCAAI,OAAO,IAAI;;;;;;;;uBAb1D,OAAO,GAAG;;;;;gBAiBxB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACpD,WAAU;;kDAEV,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;kDACpB,6LAAC;wCAAO,OAAM;kDAAK;;;;;;kDACnB,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;;;;;;;;kCAIxB,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC,sKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,6LAAC,gKAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,6LAAC,wJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;8DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;8DACR,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAc,QAAO;oDAAU,MAAK;;;;;;8DAClE,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAW,QAAO;oDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMrE,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC,sKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,6LAAC,gKAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,6LAAC,wJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;8DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;8DACR,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAK,QAAO;oDAAU,MAAK;;;;;;8DACzD,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAK,QAAO;oDAAU,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAM/D,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC,sKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,6LAAC,gKAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,6LAAC,wJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;8DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;8DACR,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAM,QAAO;oDAAU,MAAK;oDAAU,aAAa;oDAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;0CAMjG,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC,sKAAA,CAAA,sBAAmB;wCAAC,OAAM;wCAAO,QAAQ;kDACxC,cAAA,6LAAC,wJAAA,CAAA,YAAS;4CAAC,MAAM;;8DACf,6LAAC,gKAAA,CAAA,gBAAa;oDAAC,iBAAgB;;;;;;8DAC/B,6LAAC,wJAAA,CAAA,QAAK;oDAAC,SAAQ;;;;;;8DACf,6LAAC,wJAAA,CAAA,QAAK;;;;;8DACN,6LAAC,0JAAA,CAAA,UAAO;;;;;8DACR,6LAAC,uJAAA,CAAA,OAAI;oDAAC,MAAK;oDAAW,SAAQ;oDAAU,QAAO;oDAAU,MAAK;oDAAU,aAAa;oDAAK,MAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzG,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;oCAAM,WAAU;8CACf,cAAA,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;0DAG/F,6LAAC;gDAAG,WAAU;0DAAiF;;;;;;;;;;;;;;;;;8CAKnG,6LAAC;oCAAM,WAAU;8CACd,QAAQ,GAAG,CAAC,CAAC;wCACZ,MAAM,OAAO,cAAc,OAAO,IAAI;wCAEtC,qBACE,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;;;;;0EAChB,6LAAC;;kFACC,6LAAC;wEAAI,WAAU;kFAAqC,OAAO,IAAI;;;;;;kFAC/D,6LAAC;wEAAI,WAAU;kFAAyB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;8DAIvD,6LAAC;oDAAG,WAAU;8DACX,OAAO,IAAI,KAAK,gBAAgB,aAChC,OAAO,IAAI,KAAK,aAAa,QAC7B,OAAO,IAAI,KAAK,OAAO,OACvB,OAAO,IAAI,KAAK,OAAO,OACvB,OAAO,IAAI,KAAK,QAAQ,QAAQ;;;;;;8DAEnC,6LAAC;oDAAG,WAAU;8DACX,OAAO,QAAQ;;;;;;8DAElB,6LAAC;oDAAG,WAAU;8DACZ,cAAA,6LAAC;wDAAK,WAAW,AAAC,2EAAwG,OAA9B,eAAe,OAAO,MAAM;kEACrH,OAAO,MAAM,KAAK,WAAW,cAC7B,OAAO,MAAM,KAAK,YAAY,UAAU;;;;;;;;;;;8DAG7C,6LAAC;oDAAG,WAAU;8DACX,OAAO,WAAW;;;;;;;2CA3Bd,OAAO,EAAE;;;;;oCA+BtB;;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAsC;;;;;;0DACnD,6LAAC;gDAAE,WAAU;0DAA0B;;;;;;;;;;;;;;;;;;0CAI3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,uNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;0DAAmC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlD;GA3UwB;KAAA", "debugId": null}}, {"offset": {"line": 4161, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/components/AutomationSystem.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Setting<PERSON>, Play, Pause, AlertTriangle, CheckCir<PERSON>, Clock, Zap } from 'lucide-react';\n\ninterface AutomationRule {\n  id: string;\n  name: string;\n  condition: string;\n  action: string;\n  isActive: boolean;\n  priority: 'low' | 'medium' | 'high';\n  lastTriggered?: string;\n  triggerCount: number;\n}\n\ninterface SystemStatus {\n  ledControl: boolean;\n  ventilation: boolean;\n  irrigation: boolean;\n  heating: boolean;\n  cooling: boolean;\n  co2Injection: boolean;\n}\n\nexport default function AutomationSystem() {\n  const [automationEnabled, setAutomationEnabled] = useState(true);\n  const [systemStatus, setSystemStatus] = useState<SystemStatus>({\n    ledControl: true,\n    ventilation: true,\n    irrigation: false,\n    heating: false,\n    cooling: false,\n    co2Injection: true\n  });\n\n  const [rules, setRules] = useState<AutomationRule[]>([\n    {\n      id: 'R001',\n      name: '<PERSON><PERSON>cak<PERSON><PERSON><PERSON>rolü - Soğutma',\n      condition: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> > 25°C',\n      action: 'Soğutma sistemini aç, LED gücünü %10 azalt',\n      isActive: true,\n      priority: 'high',\n      lastTriggered: '2024-01-29 14:30',\n      triggerCount: 3\n    },\n    {\n      id: 'R002',\n      name: 'Sıcaklık Kontrolü - Isıtma',\n      condition: 'Sıcaklık < 18°C',\n      action: 'Isıtma sistemini aç',\n      isActive: true,\n      priority: 'high',\n      triggerCount: 0\n    },\n    {\n      id: 'R003',\n      name: 'Nem Kontrolü',\n      condition: 'Nem < 55% veya Nem > 75%',\n      action: 'Ventilasyon hızını ayarla',\n      isActive: true,\n      priority: 'medium',\n      lastTriggered: '2024-01-29 12:15',\n      triggerCount: 7\n    },\n    {\n      id: 'R004',\n      name: 'pH Düzeltme',\n      condition: 'pH < 5.5 veya pH > 6.8',\n      action: 'pH düzeltme pompasını çalıştır',\n      isActive: true,\n      priority: 'high',\n      lastTriggered: '2024-01-29 09:45',\n      triggerCount: 2\n    },\n    {\n      id: 'R005',\n      name: 'EC Kontrolü',\n      condition: 'EC < 1.2 veya EC > 2.0',\n      action: 'Besin çözeltisi konsantrasyonunu ayarla',\n      isActive: true,\n      priority: 'medium',\n      triggerCount: 1\n    },\n    {\n      id: 'R006',\n      name: 'PPFD Optimizasyonu',\n      condition: 'PPFD > 350 µmol/m²/s',\n      action: 'LED mesafesini artır veya gücü azalt',\n      isActive: true,\n      priority: 'medium',\n      lastTriggered: '2024-01-29 16:20',\n      triggerCount: 5\n    },\n    {\n      id: 'R007',\n      name: 'CO₂ Enjeksiyonu',\n      condition: 'CO₂ < 600 ppm ve LED açık',\n      action: 'CO₂ enjeksiyon sistemini aç',\n      isActive: false,\n      priority: 'low',\n      triggerCount: 0\n    },\n    {\n      id: 'R008',\n      name: 'Gece Modu',\n      condition: 'Saat 20:00 - 06:00',\n      action: 'LED\\'leri kapat, ventilasyonu azalt',\n      isActive: true,\n      priority: 'low',\n      lastTriggered: '2024-01-28 20:00',\n      triggerCount: 15\n    }\n  ]);\n\n  const [recentActions, setRecentActions] = useState([\n    { id: 1, time: '14:35', action: 'LED gücü %75\\'e düşürüldü', type: 'automatic', rule: 'R001' },\n    { id: 2, time: '14:30', action: 'Soğutma sistemi devreye alındı', type: 'automatic', rule: 'R001' },\n    { id: 3, time: '12:15', action: 'Ventilasyon hızı artırıldı', type: 'automatic', rule: 'R003' },\n    { id: 4, time: '11:45', action: 'Sistem manuel olarak durduruldu', type: 'manual', rule: null },\n    { id: 5, time: '09:45', action: 'pH düzeltme pompası çalıştırıldı', type: 'automatic', rule: 'R004' }\n  ]);\n\n  const toggleRule = (ruleId: string) => {\n    setRules(prev => prev.map(rule => \n      rule.id === ruleId ? { ...rule, isActive: !rule.isActive } : rule\n    ));\n  };\n\n  const getPriorityColor = (priority: string) => {\n    switch (priority) {\n      case 'high': return 'text-red-600 bg-red-100';\n      case 'medium': return 'text-yellow-600 bg-yellow-100';\n      case 'low': return 'text-green-600 bg-green-100';\n      default: return 'text-gray-600 bg-gray-100';\n    }\n  };\n\n  const getSystemStatusColor = (status: boolean) => {\n    return status ? 'bg-green-500' : 'bg-gray-400';\n  };\n\n  const activeRules = rules.filter(rule => rule.isActive).length;\n  const totalTriggers = rules.reduce((sum, rule) => sum + rule.triggerCount, 0);\n\n  // Simüle edilmiş sistem durumu güncellemesi\n  useEffect(() => {\n    const interval = setInterval(() => {\n      if (automationEnabled) {\n        // Rastgele sistem durumu değişiklikleri\n        setSystemStatus(prev => ({\n          ...prev,\n          irrigation: Math.random() > 0.7,\n          heating: Math.random() > 0.8,\n          cooling: Math.random() > 0.9\n        }));\n      }\n    }, 5000);\n\n    return () => clearInterval(interval);\n  }, [automationEnabled]);\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Otomasyon Kontrolü */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div>\n            <h2 className=\"text-lg font-semibold text-gray-900\">Otomasyon Sistemi</h2>\n            <p className=\"text-sm text-gray-500\">Kozai parametrelerine göre otomatik kontrol</p>\n          </div>\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">Otomasyon</span>\n              <button\n                onClick={() => setAutomationEnabled(!automationEnabled)}\n                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${\n                  automationEnabled ? 'bg-green-600' : 'bg-gray-200'\n                }`}\n              >\n                <span\n                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${\n                    automationEnabled ? 'translate-x-6' : 'translate-x-1'\n                  }`}\n                />\n              </button>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              {automationEnabled ? (\n                <Play className=\"w-5 h-5 text-green-600\" />\n              ) : (\n                <Pause className=\"w-5 h-5 text-gray-400\" />\n              )}\n              <span className={`text-sm font-medium ${\n                automationEnabled ? 'text-green-600' : 'text-gray-400'\n              }`}>\n                {automationEnabled ? 'Aktif' : 'Durduruldu'}\n              </span>\n            </div>\n          </div>\n        </div>\n\n        {/* Özet Kartları */}\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6\">\n          <div className=\"bg-blue-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Settings className=\"w-5 h-5 text-blue-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Aktif Kurallar</span>\n            </div>\n            <div className=\"text-2xl font-bold text-blue-600 mt-1\">{activeRules}/{rules.length}</div>\n          </div>\n\n          <div className=\"bg-green-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <CheckCircle className=\"w-5 h-5 text-green-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Toplam Tetikleme</span>\n            </div>\n            <div className=\"text-2xl font-bold text-green-600 mt-1\">{totalTriggers}</div>\n          </div>\n\n          <div className=\"bg-purple-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Clock className=\"w-5 h-5 text-purple-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Son İşlem</span>\n            </div>\n            <div className=\"text-sm font-bold text-purple-600 mt-1\">14:35</div>\n          </div>\n\n          <div className=\"bg-orange-50 rounded-lg p-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Zap className=\"w-5 h-5 text-orange-600\" />\n              <span className=\"text-sm font-medium text-gray-700\">Sistem Durumu</span>\n            </div>\n            <div className=\"text-sm font-bold text-orange-600 mt-1\">\n              {Object.values(systemStatus).filter(Boolean).length}/6 Aktif\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Sistem Durumu */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Sistem Bileşenleri</h3>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4\">\n          {[\n            { key: 'ledControl', name: 'LED Kontrol', icon: '💡' },\n            { key: 'ventilation', name: 'Ventilasyon', icon: '🌪️' },\n            { key: 'irrigation', name: 'Sulama', icon: '💧' },\n            { key: 'heating', name: 'Isıtma', icon: '🔥' },\n            { key: 'cooling', name: 'Soğutma', icon: '❄️' },\n            { key: 'co2Injection', name: 'CO₂ Enjeksiyon', icon: '🌱' }\n          ].map((system) => (\n            <div key={system.key} className=\"bg-gray-50 rounded-lg p-4 text-center\">\n              <div className=\"text-2xl mb-2\">{system.icon}</div>\n              <div className=\"text-sm font-medium text-gray-700 mb-2\">{system.name}</div>\n              <div className={`w-3 h-3 rounded-full mx-auto ${\n                getSystemStatusColor(systemStatus[system.key as keyof SystemStatus])\n              }`}></div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Otomasyon Kuralları */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Otomasyon Kuralları</h3>\n        <div className=\"space-y-4\">\n          {rules.map((rule) => (\n            <div key={rule.id} className={`border rounded-lg p-4 ${\n              rule.isActive ? 'border-green-200 bg-green-50' : 'border-gray-200 bg-gray-50'\n            }`}>\n              <div className=\"flex items-center justify-between mb-2\">\n                <div className=\"flex items-center space-x-3\">\n                  <button\n                    onClick={() => toggleRule(rule.id)}\n                    className={`w-4 h-4 rounded border-2 flex items-center justify-center ${\n                      rule.isActive ? 'bg-green-500 border-green-500' : 'border-gray-300'\n                    }`}\n                  >\n                    {rule.isActive && <CheckCircle className=\"w-3 h-3 text-white\" />}\n                  </button>\n                  <h4 className=\"font-medium text-gray-900\">{rule.name}</h4>\n                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(rule.priority)}`}>\n                    {rule.priority === 'high' ? 'Yüksek' : \n                     rule.priority === 'medium' ? 'Orta' : 'Düşük'}\n                  </span>\n                </div>\n                <div className=\"text-sm text-gray-500\">\n                  {rule.triggerCount} kez tetiklendi\n                </div>\n              </div>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm\">\n                <div>\n                  <span className=\"font-medium text-gray-700\">Koşul: </span>\n                  <span className=\"text-gray-600\">{rule.condition}</span>\n                </div>\n                <div>\n                  <span className=\"font-medium text-gray-700\">Eylem: </span>\n                  <span className=\"text-gray-600\">{rule.action}</span>\n                </div>\n              </div>\n              \n              {rule.lastTriggered && (\n                <div className=\"mt-2 text-xs text-gray-500\">\n                  Son tetikleme: {rule.lastTriggered}\n                </div>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Son İşlemler */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Son İşlemler</h3>\n        <div className=\"space-y-3\">\n          {recentActions.map((action) => (\n            <div key={action.id} className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <div className=\"flex-shrink-0\">\n                {action.type === 'automatic' ? (\n                  <div className=\"w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center\">\n                    <Settings className=\"w-4 h-4 text-blue-600\" />\n                  </div>\n                ) : (\n                  <div className=\"w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center\">\n                    <Settings className=\"w-4 h-4 text-gray-600\" />\n                  </div>\n                )}\n              </div>\n              <div className=\"flex-1\">\n                <div className=\"flex items-center justify-between\">\n                  <p className=\"text-sm font-medium text-gray-900\">{action.action}</p>\n                  <span className=\"text-xs text-gray-500\">{action.time}</span>\n                </div>\n                <div className=\"flex items-center space-x-2 mt-1\">\n                  <span className={`px-2 py-1 text-xs rounded-full ${\n                    action.type === 'automatic' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'\n                  }`}>\n                    {action.type === 'automatic' ? 'Otomatik' : 'Manuel'}\n                  </span>\n                  {action.rule && (\n                    <span className=\"text-xs text-gray-500\">Kural: {action.rule}</span>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Kozai Önerileri */}\n      <div className=\"bg-white rounded-lg shadow p-6\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">Kozai Otomasyon Önerileri</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"space-y-3\">\n            <h4 className=\"font-medium text-gray-800\">Kritik Parametreler</h4>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Sıcaklık: 20-24°C aralığında tut</li>\n              <li>• PPFD: 250-300 µmol/m²/s optimal</li>\n              <li>• pH: 5.8-6.3 aralığında kontrol et</li>\n              <li>• EC: 1.4-1.8 mS/cm seviyesinde</li>\n            </ul>\n          </div>\n          <div className=\"space-y-3\">\n            <h4 className=\"font-medium text-gray-800\">Enerji Optimizasyonu</h4>\n            <ul className=\"text-sm text-gray-600 space-y-1\">\n              <li>• Gece modunda LED'leri kapat</li>\n              <li>• Sıcaklık yüksekse LED gücünü azalt</li>\n              <li>• CO₂ yoksa ışık seviyesini düşür</li>\n              <li>• Ventilasyonu nem seviyesine göre ayarla</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAyBe,SAAS;;IACtB,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB;QAC7D,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,SAAS;QACT,SAAS;QACT,cAAc;IAChB;IAEA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;QACnD;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YAC<PERSON>,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,cAAc;QAChB;QACA;YACE,IAAI;YACJ,MAAM;YACN,WAAW;YACX,QAAQ;YACR,UAAU;YACV,UAAU;YACV,eAAe;YACf,cAAc;QAChB;KACD;IAED,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjD;YAAE,IAAI;YAAG,MAAM;YAAS,QAAQ;YAA6B,MAAM;YAAa,MAAM;QAAO;QAC7F;YAAE,IAAI;YAAG,MAAM;YAAS,QAAQ;YAAkC,MAAM;YAAa,MAAM;QAAO;QAClG;YAAE,IAAI;YAAG,MAAM;YAAS,QAAQ;YAA8B,MAAM;YAAa,MAAM;QAAO;QAC9F;YAAE,IAAI;YAAG,MAAM;YAAS,QAAQ;YAAmC,MAAM;YAAU,MAAM;QAAK;QAC9F;YAAE,IAAI;YAAG,MAAM;YAAS,QAAQ;YAAoC,MAAM;YAAa,MAAM;QAAO;KACrG;IAED,MAAM,aAAa,CAAC;QAClB,SAAS,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,OACxB,KAAK,EAAE,KAAK,SAAS;oBAAE,GAAG,IAAI;oBAAE,UAAU,CAAC,KAAK,QAAQ;gBAAC,IAAI;IAEjE;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,SAAS,iBAAiB;IACnC;IAEA,MAAM,cAAc,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;IAC9D,MAAM,gBAAgB,MAAM,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,YAAY,EAAE;IAE3E,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR,MAAM,WAAW;uDAAY;oBAC3B,IAAI,mBAAmB;wBACrB,wCAAwC;wBACxC;mEAAgB,CAAA,OAAQ,CAAC;oCACvB,GAAG,IAAI;oCACP,YAAY,KAAK,MAAM,KAAK;oCAC5B,SAAS,KAAK,MAAM,KAAK;oCACzB,SAAS,KAAK,MAAM,KAAK;gCAC3B,CAAC;;oBACH;gBACF;sDAAG;YAEH;8CAAO,IAAM,cAAc;;QAC7B;qCAAG;QAAC;KAAkB;IAEtB,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAEvC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,6LAAC;gDACC,SAAS,IAAM,qBAAqB,CAAC;gDACrC,WAAW,AAAC,6EAEX,OADC,oBAAoB,iBAAiB;0DAGvC,cAAA,6LAAC;oDACC,WAAW,AAAC,6EAEX,OADC,oBAAoB,kBAAkB;;;;;;;;;;;;;;;;;kDAK9C,6LAAC;wCAAI,WAAU;;4CACZ,kCACC,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;qEAEhB,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DAEnB,6LAAC;gDAAK,WAAW,AAAC,uBAEjB,OADC,oBAAoB,mBAAmB;0DAEtC,oBAAoB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAOvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;4CAAyC;4CAAY;4CAAE,MAAM,MAAM;;;;;;;;;;;;;0CAGpF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDAA0C;;;;;;;;;;;;0CAG3D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDAAyC;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;0DACf,6LAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,MAAM,CAAC,cAAc,MAAM,CAAC,SAAS,MAAM;4CAAC;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ;4BACC;gCAAE,KAAK;gCAAc,MAAM;gCAAe,MAAM;4BAAK;4BACrD;gCAAE,KAAK;gCAAe,MAAM;gCAAe,MAAM;4BAAM;4BACvD;gCAAE,KAAK;gCAAc,MAAM;gCAAU,MAAM;4BAAK;4BAChD;gCAAE,KAAK;gCAAW,MAAM;gCAAU,MAAM;4BAAK;4BAC7C;gCAAE,KAAK;gCAAW,MAAM;gCAAW,MAAM;4BAAK;4BAC9C;gCAAE,KAAK;gCAAgB,MAAM;gCAAkB,MAAM;4BAAK;yBAC3D,CAAC,GAAG,CAAC,CAAC,uBACL,6LAAC;gCAAqB,WAAU;;kDAC9B,6LAAC;wCAAI,WAAU;kDAAiB,OAAO,IAAI;;;;;;kDAC3C,6LAAC;wCAAI,WAAU;kDAA0C,OAAO,IAAI;;;;;;kDACpE,6LAAC;wCAAI,WAAW,AAAC,gCAEhB,OADC,qBAAqB,YAAY,CAAC,OAAO,GAAG,CAAuB;;;;;;;+BAJ7D,OAAO,GAAG;;;;;;;;;;;;;;;;0BAY1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,6LAAC;gCAAkB,WAAW,AAAC,yBAE9B,OADC,KAAK,QAAQ,GAAG,iCAAiC;;kDAEjD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,SAAS,IAAM,WAAW,KAAK,EAAE;wDACjC,WAAW,AAAC,6DAEX,OADC,KAAK,QAAQ,GAAG,kCAAkC;kEAGnD,KAAK,QAAQ,kBAAI,6LAAC,8NAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;;;;;;kEAE3C,6LAAC;wDAAG,WAAU;kEAA6B,KAAK,IAAI;;;;;;kEACpD,6LAAC;wDAAK,WAAW,AAAC,8CAA6E,OAAhC,iBAAiB,KAAK,QAAQ;kEAC1F,KAAK,QAAQ,KAAK,SAAS,WAC3B,KAAK,QAAQ,KAAK,WAAW,SAAS;;;;;;;;;;;;0DAG3C,6LAAC;gDAAI,WAAU;;oDACZ,KAAK,YAAY;oDAAC;;;;;;;;;;;;;kDAIvB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAiB,KAAK,SAAS;;;;;;;;;;;;0DAEjD,6LAAC;;kEACC,6LAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAC5C,6LAAC;wDAAK,WAAU;kEAAiB,KAAK,MAAM;;;;;;;;;;;;;;;;;;oCAI/C,KAAK,aAAa,kBACjB,6LAAC;wCAAI,WAAU;;4CAA6B;4CAC1B,KAAK,aAAa;;;;;;;;+BArC9B,KAAK,EAAE;;;;;;;;;;;;;;;;0BA8CvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;kCACZ,cAAc,GAAG,CAAC,CAAC,uBAClB,6LAAC;gCAAoB,WAAU;;kDAC7B,6LAAC;wCAAI,WAAU;kDACZ,OAAO,IAAI,KAAK,4BACf,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;iEAGtB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAI1B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,OAAO,MAAM;;;;;;kEAC/D,6LAAC;wDAAK,WAAU;kEAAyB,OAAO,IAAI;;;;;;;;;;;;0DAEtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAW,AAAC,kCAEjB,OADC,OAAO,IAAI,KAAK,cAAc,8BAA8B;kEAE3D,OAAO,IAAI,KAAK,cAAc,aAAa;;;;;;oDAE7C,OAAO,IAAI,kBACV,6LAAC;wDAAK,WAAU;;4DAAwB;4DAAQ,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;+BAxBzD,OAAO,EAAE;;;;;;;;;;;;;;;;0BAkCzB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;0CAGR,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA4B;;;;;;kDAC1C,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAjWwB;KAAA", "debugId": null}}, {"offset": {"line": 5179, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/hydroponic_indoor/hydroponic-system/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Thermometer, Droplets, Lightbulb, Zap, Leaf, Settings, Calculator } from 'lucide-react';\nimport Dashboard from '@/components/Dashboard';\nimport LEDControl from '@/components/LEDControl';\nimport ProductionTracker from '@/components/ProductionTracker';\nimport EnergyCalculator from '@/components/EnergyCalculator';\nimport SensorData from '@/components/SensorData';\nimport AutomationSystem from '@/components/AutomationSystem';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState('dashboard');\n\n  const tabs = [\n    { id: 'dashboard', name: 'Dashboard', icon: Thermometer },\n    { id: 'feasibility', name: '<PERSON>zibilite', icon: Calculator },\n    { id: 'led', name: 'LED Kontrol', icon: Lightbulb },\n    { id: 'production', name: '<PERSON>retim <PERSON>ki<PERSON>', icon: Leaf },\n    { id: 'energy', name: '<PERSON><PERSON><PERSON>', icon: Zap },\n    { id: 'sensors', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: Droplets },\n    { id: 'automation', name: '<PERSON><PERSON><PERSON>yon', icon: Settings },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center\">\n                <Leaf className=\"w-5 h-5 text-white\" />\n              </div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">Hidroponik Sistem</h1>\n                <p className=\"text-sm text-gray-500\">Kozai Yaklaşımı ile Akıllı Tarım</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 bg-green-500 rounded-full animate-pulse\"></div>\n              <span className=\"text-sm text-gray-600\">Sistem Aktif</span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Navigation */}\n      <nav className=\"bg-white border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex space-x-8 overflow-x-auto\">\n            {tabs.map((tab) => {\n              const Icon = tab.icon;\n              return (\n                <button\n                  key={tab.id}\n                  onClick={() => setActiveTab(tab.id)}\n                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${\n                    activeTab === tab.id\n                      ? 'border-green-500 text-green-600'\n                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'\n                  }`}\n                >\n                  <Icon className=\"w-4 h-4\" />\n                  <span>{tab.name}</span>\n                </button>\n              );\n            })}\n          </div>\n        </div>\n      </nav>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {activeTab === 'dashboard' && <Dashboard />}\n        {activeTab === 'feasibility' && (\n          <div className=\"p-6\">\n            <iframe\n              src=\"/feasibility\"\n              className=\"w-full h-screen border-0 rounded-lg\"\n              title=\"Fizibilite Analizi\"\n            />\n          </div>\n        )}\n        {activeTab === 'led' && <LEDControl />}\n        {activeTab === 'production' && <ProductionTracker />}\n        {activeTab === 'energy' && <EnergyCalculator />}\n        {activeTab === 'sensors' && <SensorData />}\n        {activeTab === 'automation' && <AutomationSystem />}\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO;QACX;YAAE,IAAI;YAAa,MAAM;YAAa,MAAM,mNAAA,CAAA,cAAW;QAAC;QACxD;YAAE,IAAI;YAAe,MAAM;YAAc,MAAM,iNAAA,CAAA,aAAU;QAAC;QAC1D;YAAE,IAAI;YAAO,MAAM;YAAe,MAAM,+MAAA,CAAA,YAAS;QAAC;QAClD;YAAE,IAAI;YAAc,MAAM;YAAgB,MAAM,qMAAA,CAAA,OAAI;QAAC;QACrD;YAAE,IAAI;YAAU,MAAM;YAAiB,MAAM,mMAAA,CAAA,MAAG;QAAC;QACjD;YAAE,IAAI;YAAW,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;QACnD;YAAE,IAAI;YAAc,MAAM;YAAa,MAAM,6MAAA,CAAA,WAAQ;QAAC;KACvD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAChD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAGzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;;;;;kDACf,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhD,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAC;4BACT,MAAM,OAAO,IAAI,IAAI;4BACrB,qBACE,6LAAC;gCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;gCAClC,WAAW,AAAC,0FAIX,OAHC,cAAc,IAAI,EAAE,GAChB,oCACA;;kDAGN,6LAAC;wCAAK,WAAU;;;;;;kDAChB,6LAAC;kDAAM,IAAI,IAAI;;;;;;;+BATV,IAAI,EAAE;;;;;wBAYjB;;;;;;;;;;;;;;;;0BAMN,6LAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,6LAAC,kIAAA,CAAA,UAAS;;;;;oBACvC,cAAc,+BACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,KAAI;4BACJ,WAAU;4BACV,OAAM;;;;;;;;;;;oBAIX,cAAc,uBAAS,6LAAC,mIAAA,CAAA,UAAU;;;;;oBAClC,cAAc,8BAAgB,6LAAC,0IAAA,CAAA,UAAiB;;;;;oBAChD,cAAc,0BAAY,6LAAC,yIAAA,CAAA,UAAgB;;;;;oBAC3C,cAAc,2BAAa,6LAAC,mIAAA,CAAA,UAAU;;;;;oBACtC,cAAc,8BAAgB,6LAAC,yIAAA,CAAA,UAAgB;;;;;;;;;;;;;;;;;AAIxD;GAjFwB;KAAA", "debugId": null}}]}