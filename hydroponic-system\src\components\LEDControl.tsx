'use client';

import { useState } from 'react';
import { Lightbulb, Sun, Moon, Setting<PERSON>, Power } from 'lucide-react';

interface LEDSettings {
  power: boolean;
  intensity: number;
  distance: number;
  photoperiod: number;
  schedule: {
    start: string;
    end: string;
  };
}

export default function LEDControl() {
  const [ledSettings, setLedSettings] = useState<LEDSettings>({
    power: true,
    intensity: 75,
    distance: 35,
    photoperiod: 14,
    schedule: {
      start: '06:00',
      end: '20:00'
    }
  });

  const [autoMode, setAutoMode] = useState(true);

  // PPFD hesaplama (Samsung LM301H EVO için yaklaşık)
  const calculatePPFD = (intensity: number, distance: number) => {
    const basePPFD = 400; // 30cm mesafede %100 güçte
    const distanceFactor = Math.pow(30 / distance, 2);
    const intensityFactor = intensity / 100;
    return Math.round(basePPFD * distanceFactor * intensityFactor);
  };

  // DLI hesaplama
  const calculateDLI = (ppfd: number, hours: number) => {
    return ((ppfd * hours * 3600) / 1000000).toFixed(1);
  };

  const currentPPFD = calculatePPFD(ledSettings.intensity, ledSettings.distance);
  const currentDLI = calculateDLI(currentPPFD, ledSettings.photoperiod);

  const handleIntensityChange = (value: number) => {
    setLedSettings(prev => ({ ...prev, intensity: value }));
  };

  const handleDistanceChange = (value: number) => {
    setLedSettings(prev => ({ ...prev, distance: value }));
  };

  const handlePhotoperiodChange = (value: number) => {
    setLedSettings(prev => ({ ...prev, photoperiod: value }));
  };

  const getStatusColor = (ppfd: number) => {
    if (ppfd >= 200 && ppfd <= 300) return 'text-green-600';
    if (ppfd > 350) return 'text-red-600';
    return 'text-yellow-600';
  };

  const getRecommendation = (ppfd: number) => {
    if (ppfd > 350) return 'PPFD çok yüksek! LED mesafesini artırın veya gücü azaltın.';
    if (ppfd < 200) return 'PPFD düşük. LED mesafesini azaltın veya gücü artırın.';
    if (ppfd >= 200 && ppfd <= 300) return 'Optimal PPFD aralığında. Kozai parametrelerine uygun.';
    return 'PPFD kabul edilebilir aralıkta.';
  };

  return (
    <div className="space-y-6">
      {/* LED Durum Kartı */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-lg font-semibold text-gray-900">LED Işık Kontrolü</h2>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Otomatik Mod</span>
              <button
                onClick={() => setAutoMode(!autoMode)}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  autoMode ? 'bg-green-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    autoMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
            <button
              onClick={() => setLedSettings(prev => ({ ...prev, power: !prev.power }))}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium ${
                ledSettings.power
                  ? 'bg-green-100 text-green-700 hover:bg-green-200'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              <Power className="w-4 h-4" />
              <span>{ledSettings.power ? 'Açık' : 'Kapalı'}</span>
            </button>
          </div>
        </div>

        {/* Mevcut Değerler */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Lightbulb className="w-5 h-5 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">PPFD</span>
            </div>
            <div className={`text-2xl font-bold ${getStatusColor(currentPPFD)}`}>
              {currentPPFD} µmol/m²/s
            </div>
            <div className="text-xs text-gray-500 mt-1">Hedef: 200-300</div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Sun className="w-5 h-5 text-green-500" />
              <span className="text-sm font-medium text-gray-700">DLI</span>
            </div>
            <div className="text-2xl font-bold text-green-600">
              {currentDLI} mol/m²/gün
            </div>
            <div className="text-xs text-gray-500 mt-1">Hedef: 12-17</div>
          </div>

          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center space-x-2 mb-2">
              <Settings className="w-5 h-5 text-purple-500" />
              <span className="text-sm font-medium text-gray-700">Güç Tüketimi</span>
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {((ledSettings.intensity / 100) * 8.4).toFixed(1)} kW
            </div>
            <div className="text-xs text-gray-500 mt-1">Maksimum: 8.4 kW</div>
          </div>
        </div>

        {/* Öneri */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <p className="text-sm text-yellow-800">{getRecommendation(currentPPFD)}</p>
        </div>
      </div>

      {/* Kontrol Paneli */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Manuel Kontroller</h3>
        
        <div className="space-y-6">
          {/* LED Yoğunluğu */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LED Yoğunluğu: {ledSettings.intensity}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={ledSettings.intensity}
              onChange={(e) => handleIntensityChange(Number(e.target.value))}
              disabled={autoMode}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0%</span>
              <span>50%</span>
              <span>100%</span>
            </div>
          </div>

          {/* LED Mesafesi */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              LED Mesafesi: {ledSettings.distance} cm
            </label>
            <input
              type="range"
              min="20"
              max="50"
              value={ledSettings.distance}
              onChange={(e) => handleDistanceChange(Number(e.target.value))}
              disabled={autoMode}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>20 cm</span>
              <span>35 cm</span>
              <span>50 cm</span>
            </div>
          </div>

          {/* Fotoperiyot */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Fotoperiyot: {ledSettings.photoperiod} saat
            </label>
            <input
              type="range"
              min="8"
              max="18"
              value={ledSettings.photoperiod}
              onChange={(e) => handlePhotoperiodChange(Number(e.target.value))}
              disabled={autoMode}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>8h</span>
              <span>14h</span>
              <span>18h</span>
            </div>
          </div>

          {/* Zamanlama */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Başlangıç Saati
              </label>
              <input
                type="time"
                value={ledSettings.schedule.start}
                onChange={(e) => setLedSettings(prev => ({
                  ...prev,
                  schedule: { ...prev.schedule, start: e.target.value }
                }))}
                disabled={autoMode}
                className="w-full px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Bitiş Saati
              </label>
              <input
                type="time"
                value={ledSettings.schedule.end}
                onChange={(e) => setLedSettings(prev => ({
                  ...prev,
                  schedule: { ...prev.schedule, end: e.target.value }
                }))}
                disabled={autoMode}
                className="w-full px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Kozai Önerileri */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Kozai Önerileri</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-green-50 rounded-lg p-4">
            <h4 className="font-medium text-green-800 mb-2">Optimal Ayarlar</h4>
            <ul className="text-sm text-green-700 space-y-1">
              <li>• PPFD: 250-300 µmol/m²/s</li>
              <li>• DLI: 12-15 mol/m²/gün</li>
              <li>• Fotoperiyot: 14-16 saat</li>
              <li>• LED mesafesi: 35-40 cm</li>
            </ul>
          </div>
          <div className="bg-blue-50 rounded-lg p-4">
            <h4 className="font-medium text-blue-800 mb-2">Enerji Tasarrufu</h4>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• PPFD'yi 200-220'ye düşür</li>
              <li>• Fotoperiyodu 12 saate indir</li>
              <li>• %60-80 güç aralığı kullan</li>
              <li>• CO₂ yoksa ışığı kıs</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
