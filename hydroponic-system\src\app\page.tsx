'use client';

import { useState, useEffect } from 'react';
import { Thermometer, Droplets, Lightbulb, Zap, Leaf, Settings, Calculator } from 'lucide-react';
import Dashboard from '@/components/Dashboard';
import LEDControl from '@/components/LEDControl';
import ProductionTracker from '@/components/ProductionTracker';
import EnergyCalculator from '@/components/EnergyCalculator';
import SensorData from '@/components/SensorData';
import AutomationSystem from '@/components/AutomationSystem';

export default function Home() {
  const [activeTab, setActiveTab] = useState('dashboard');

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Thermometer },
    { id: 'feasibility', name: '<PERSON>zibilite', icon: Calculator },
    { id: 'led', name: 'LED Kontrol', icon: Lightbulb },
    { id: 'production', name: '<PERSON>retim <PERSON>ki<PERSON>', icon: Leaf },
    { id: 'energy', name: '<PERSON><PERSON><PERSON>', icon: Zap },
    { id: 'sensors', name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', icon: Droplets },
    { id: 'automation', name: '<PERSON><PERSON><PERSON>yon', icon: Settings },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-green-600 rounded-lg flex items-center justify-center">
                <Leaf className="w-5 h-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">Hidroponik Sistem</h1>
                <p className="text-sm text-gray-500">Kozai Yaklaşımı ile Akıllı Tarım</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
              <span className="text-sm text-gray-600">Sistem Aktif</span>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8 overflow-x-auto">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && <Dashboard />}
        {activeTab === 'feasibility' && (
          <div className="p-6">
            <iframe
              src="/feasibility"
              className="w-full h-screen border-0 rounded-lg"
              title="Fizibilite Analizi"
            />
          </div>
        )}
        {activeTab === 'led' && <LEDControl />}
        {activeTab === 'production' && <ProductionTracker />}
        {activeTab === 'energy' && <EnergyCalculator />}
        {activeTab === 'sensors' && <SensorData />}
        {activeTab === 'automation' && <AutomationSystem />}
      </main>
    </div>
  );
}
